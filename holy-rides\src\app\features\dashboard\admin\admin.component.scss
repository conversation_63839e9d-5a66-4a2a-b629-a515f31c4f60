.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
    background-color: #f5f5f5;
}

.dashboard-title {
  margin-bottom: 20px;
  color: #3f51b5;
  font-weight: 500;
}

.tab-content {
  padding: 20px 0;
  overflow-y: auto;

}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
  align-items: center;
}

.mat-form-field {
  flex: 1;
  min-width: 200px;
}

.table-container {
  overflow-x: auto;
  margin-top: 20px;
}

table {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.54);
}

.stats-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.stats-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.full-width {
  width: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
  padding: 16px;
}

.scrollable-content {
  overflow-y: auto;
  max-height: calc(100% - 60px); /* Account for header height */
  padding-right: 8px;
}

mat-card-content {
  overflow-y: auto;
  flex: 1;
}
.ride-management-header {
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.mat-mdc-card-header {
    display: flex
;
    padding: 16px 16px !important;
}
.stat-item {
  text-align: center;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(63, 81, 181, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: 500;
  color: #1976d2;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.54);
  margin-top: 4px;
}

.ride-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow-y: auto;
}

/* Desktop/Mobile View Toggle */
.desktop-view {
  display: block;
}

.mobile-view {
  display: none;
}

/* Filter Toggle */
.desktop-filters {
  display: block;
}

.mobile-filter-container {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .mat-form-field {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Mobile-specific styles for screens less than 600px */
@media (max-width: 600px) {
  .desktop-view {
    display: none;
  }

  .mobile-view {
    display: block;
  }

  .desktop-filters {
    display: none;
  }

  .mobile-filter-container {
    display: block;
    margin-bottom: 16px;
  }

  .dashboard-container {
    padding: 10px;
  }

  .tab-content {
    padding: 10px 0;
  }

  .filters-container {
    margin-bottom: 16px;
  }

  .mat-tab-body-content {
    overflow: visible;
    min-height: 100vh;
  }

  mat-tab-group {
    min-height: 100vh;
  }

  .tab-content {
    min-height: calc(100vh - 200px);
  }

  mat-card-content {
    min-height: 300px;
    overflow: visible;
  }
}

.mat-grid-tile{
  min-height: 260px;
}

.refresh-info {
  display: flex;
  align-items: center;
  margin-left: auto;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.last-refresh {
  margin-right: 8px;
}

.auto-refresh-note {
  margin-left: 8px;
  font-style: italic;
  font-size: 12px;
}

/* Hide refresh info on mobile */
@media (max-width: 600px) {
  .refresh-info {
    display: none;
  }
}

.action-buttons {
  margin-left: 20px;
  display: flex;
  align-items: center;
}
.close-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000; /* Higher than overlay/dialog */
  cursor: pointer;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile Accordion Styles */
.mobile-view .mat-expansion-panel {
  margin: 8px 0;
  border-radius: 8px;
}

.mobile-view .mat-expansion-panel-header {
  font-size: 14px;
  padding: 12px 16px;
}

.mobile-view .mat-panel-title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.mobile-view .mat-panel-description {
  justify-content: flex-end;
  align-items: center;
  margin-left: 16px;
}

.mobile-view .user-actions-header,
.mobile-view .ride-actions-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-view .role-chip,
.mobile-view .status-chip {
  font-size: 12px;
  min-height: 24px;
  line-height: 24px;
}

.mobile-view .user-details,
.mobile-view .ride-details {
  padding: 0 24px 16px;
  font-size: 14px;
}

.mobile-view .user-details p,
.mobile-view .ride-details p {
  margin: 8px 0;
  line-height: 1.4;
}

.mobile-view .mat-action-row {
  justify-content: flex-end;
  padding: 8px 12px 8px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.mobile-view .mat-action-row button {
  margin-left: 8px;
}

/* Mobile Filter Buttons */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 16px;
}

.filter-button {
  font-size: 12px;
  min-width: auto;
  padding: 0 12px;
  height: 32px;
  border-radius: 16px;
}

.filter-button.mat-stroked-button[color="primary"] {
  background-color: #3f51b5;
  color: white;
  border-color: #3f51b5;
}

.filter-button.mat-stroked-button {
  border-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.87);
}

/* Mobile Search */
.mobile-search {
  padding: 8px 16px 0 16px;

  mat-form-field {
    width: 100%;
    font-size: 14px;
  }

  .mat-mdc-form-field {
    margin-bottom: 0;
  }

  .mat-mdc-text-field-wrapper {
    height: 40px;
  }

  input {
    font-size: 14px;
  }
}