import{c as mn}from"./chunk-VICLKD4Y.js";import{a as Bi,b as Ui,c as zi,d as <PERSON>,e as Hi}from"./chunk-OCX3SM77.js";import{A as Fi,h as xe,i as ct,j as Ci,k as yi,l as Me,m as De,n as ke,o as ae,r as Ri,u as jt,v as Ei,w as Pi,x as Ti,y as Ii,z as Ai}from"./chunk-KJHHDGHU.js";import"./chunk-MS4AQ6UA.js";import{a as on,b as sn,c as ln,d as dn,e as cn}from"./chunk-LMT2ZV2T.js";import{b as an}from"./chunk-EMQER2I7.js";import{A as Yi,B as en,C as tn,D as nn,E as rn,a as qi,b as Ne,c as Gi,d as Qi,e as Wi,f as Xi,g as Ji,h as gt,j as _t,k as ft,l as ht,m as vt,n as wt,o as St,p as bt,q as Ct,r as yt,s as xt,t as Mt,u as Le,v as Gt,w as Dt,x as kt,y as Ki,z as Zi}from"./chunk-HEB4POL5.js";import{a as pe,b as ue,c as ge,d as ji,e as $e}from"./chunk-YSBVITGM.js";import{a as $i,b as Li}from"./chunk-OFTCLERB.js";import"./chunk-EACHO2FA.js";import"./chunk-ORDMVBIZ.js";import{a as de,b as J}from"./chunk-WSXVBUWR.js";import{a as Z}from"./chunk-IT7B5FSQ.js";import{a as Ae,c as Oe,d as le}from"./chunk-3MEMYPGR.js";import"./chunk-EO3PEVGH.js";import{a as $,b as re}from"./chunk-553Y2ERR.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{C as W,F as X,H as ce,I as me,J as Oi,a as xi,b as oe,c as Mi,d as F,f as H,g as Re,j as mt,k as Ee,l as pt,n as Pe,o as Te,p as Di,q as ki,s as Ie,t as G,u as se,w as Q,x as Fe,z as ut}from"./chunk-AG3SD6JT.js";import{$b as st,Ab as We,Ad as Si,Ba as fe,Bb as Xe,Bd as dt,Cd as bi,Db as ui,Ea as di,Eb as Y,Ed as ye,Fb as L,Fc as U,Gb as q,Gd as z,Jb as ee,Jd as N,Kb as s,Kd as Ht,La as d,Lb as D,Ld as Ve,Mb as S,Md as Be,Nd as Ue,Ob as te,Od as Vi,Pa as x,Pb as ie,Qb as ne,Qd as ze,Rd as K,Sb as ot,Tb as Lt,Tc as lt,Wa as O,Wc as fi,Xa as at,Y as ri,Yc as hi,Z as ai,_ as it,aa as nt,ab as p,ca as rt,da as B,dc as gi,fc as ve,g as ni,gb as he,gc as _i,hb as c,hd as vi,ic as qt,jb as Qe,ka as oi,kb as ci,la as g,ma as _,mb as mi,na as si,qb as r,rb as a,sa as li,sb as v,tb as T,ub as I,uc as Ce,va as $t,vc as j,wb as k,xb as pi,yb as f,yd as wi,za as Ge,zb as u}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{a as je,b as He,i as M}from"./chunk-ODN5LVDJ.js";var Rt=class{tracker;columnIndex=0;rowIndex=0;get rowCount(){return this.rowIndex+1}get rowspan(){let t=Math.max(...this.tracker);return t>1?this.rowCount+t-1:this.rowCount}positions;update(t,e){this.columnIndex=0,this.rowIndex=0,this.tracker=new Array(t),this.tracker.fill(0,0,this.tracker.length),this.positions=e.map(i=>this._trackTile(i))}_trackTile(t){let e=this._findMatchingGap(t.colspan);return this._markTilePosition(e,t),this.columnIndex=e+t.colspan,new Qt(this.rowIndex,e)}_findMatchingGap(t){t>this.tracker.length;let e=-1,i=-1;do{if(this.columnIndex+t>this.tracker.length){this._nextRow(),e=this.tracker.indexOf(0,this.columnIndex),i=this._findGapEndIndex(e);continue}if(e=this.tracker.indexOf(0,this.columnIndex),e==-1){this._nextRow(),e=this.tracker.indexOf(0,this.columnIndex),i=this._findGapEndIndex(e);continue}i=this._findGapEndIndex(e),this.columnIndex=e+1}while(i-e<t||i==0);return Math.max(e,0)}_nextRow(){this.columnIndex=0,this.rowIndex++;for(let t=0;t<this.tracker.length;t++)this.tracker[t]=Math.max(0,this.tracker[t]-1)}_findGapEndIndex(t){for(let e=t+1;e<this.tracker.length;e++)if(this.tracker[e]!=0)return e;return this.tracker.length}_markTilePosition(t,e){for(let i=0;i<e.colspan;i++)this.tracker[t+i]=e.rowspan}},Qt=class{row;col;constructor(t,e){this.row=t,this.col=e}};var pn=["*"];var Mn=`.mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}
`,un=new nt("MAT_GRID_LIST"),Kt=(()=>{class n{_element=B(Ge);_gridList=B(un,{optional:!0});_rowspan=1;_colspan=1;constructor(){}get rowspan(){return this._rowspan}set rowspan(e){this._rowspan=Math.round(lt(e))}get colspan(){return this._colspan}set colspan(e){this._colspan=Math.round(lt(e))}_setStyle(e,i){this._element.nativeElement.style[e]=i}static \u0275fac=function(i){return new(i||n)};static \u0275cmp=O({type:n,selectors:[["mat-grid-tile"]],hostAttrs:[1,"mat-grid-tile"],hostVars:2,hostBindings:function(i,o){i&2&&he("rowspan",o.rowspan)("colspan",o.colspan)},inputs:{rowspan:"rowspan",colspan:"colspan"},exportAs:["matGridTile"],ngContentSelectors:pn,decls:2,vars:0,consts:[[1,"mat-grid-tile-content"]],template:function(i,o){i&1&&(We(),r(0,"div",0),Xe(1),a())},styles:[`.mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}
`],encapsulation:2,changeDetection:0})}return n})();var Dn=/^-?\d+((\.\d+)?[A-Za-z%$]?)+$/,Je=class{_gutterSize;_rows=0;_rowspan=0;_cols;_direction;init(t,e,i,o){this._gutterSize=gn(t),this._rows=e.rowCount,this._rowspan=e.rowspan,this._cols=i,this._direction=o}getBaseTileSize(t,e){return`(${t}% - (${this._gutterSize} * ${e}))`}getTilePosition(t,e){return e===0?"0":we(`(${t} + ${this._gutterSize}) * ${e}`)}getTileSize(t,e){return`(${t} * ${e}) + (${e-1} * ${this._gutterSize})`}setStyle(t,e,i){let o=100/this._cols,l=(this._cols-1)/this._cols;this.setColStyles(t,i,o,l),this.setRowStyles(t,e,o,l)}setColStyles(t,e,i,o){let l=this.getBaseTileSize(i,o),m=this._direction==="rtl"?"right":"left";t._setStyle(m,this.getTilePosition(l,e)),t._setStyle("width",we(this.getTileSize(l,t.colspan)))}getGutterSpan(){return`${this._gutterSize} * (${this._rowspan} - 1)`}getTileSpan(t){return`${this._rowspan} * ${this.getTileSize(t,1)}`}getComputedHeight(){return null}},Wt=class extends Je{fixedRowHeight;constructor(t){super(),this.fixedRowHeight=t}init(t,e,i,o){super.init(t,e,i,o),this.fixedRowHeight=gn(this.fixedRowHeight),Dn.test(this.fixedRowHeight)}setRowStyles(t,e){t._setStyle("top",this.getTilePosition(this.fixedRowHeight,e)),t._setStyle("height",we(this.getTileSize(this.fixedRowHeight,t.rowspan)))}getComputedHeight(){return["height",we(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)]}reset(t){t._setListStyle(["height",null]),t._tiles&&t._tiles.forEach(e=>{e._setStyle("top",null),e._setStyle("height",null)})}},Xt=class extends Je{rowHeightRatio;baseTileHeight;constructor(t){super(),this._parseRatio(t)}setRowStyles(t,e,i,o){let l=i/this.rowHeightRatio;this.baseTileHeight=this.getBaseTileSize(l,o),t._setStyle("marginTop",this.getTilePosition(this.baseTileHeight,e)),t._setStyle("paddingTop",we(this.getTileSize(this.baseTileHeight,t.rowspan)))}getComputedHeight(){return["paddingBottom",we(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`)]}reset(t){t._setListStyle(["paddingBottom",null]),t._tiles.forEach(e=>{e._setStyle("marginTop",null),e._setStyle("paddingTop",null)})}_parseRatio(t){let e=t.split(":");e.length,this.rowHeightRatio=parseFloat(e[0])/parseFloat(e[1])}},Jt=class extends Je{setRowStyles(t,e){let i=100/this._rowspan,o=(this._rows-1)/this._rows,l=this.getBaseTileSize(i,o);t._setStyle("top",this.getTilePosition(l,e)),t._setStyle("height",we(this.getTileSize(l,t.rowspan)))}reset(t){t._tiles&&t._tiles.forEach(e=>{e._setStyle("top",null),e._setStyle("height",null)})}};function we(n){return`calc(${n})`}function gn(n){return n.match(/([A-Za-z%]+)$/)?n:`${n}px`}var kn="fit",_n=(()=>{class n{_element=B(Ge);_dir=B(bi,{optional:!0});_cols;_tileCoordinator;_rowHeight;_gutter="1px";_tileStyler;_tiles;constructor(){}get cols(){return this._cols}set cols(e){this._cols=Math.max(1,Math.round(lt(e)))}get gutterSize(){return this._gutter}set gutterSize(e){this._gutter=`${e??""}`}get rowHeight(){return this._rowHeight}set rowHeight(e){let i=`${e??""}`;i!==this._rowHeight&&(this._rowHeight=i,this._setTileStyler(this._rowHeight))}ngOnInit(){this._checkCols(),this._checkRowHeight()}ngAfterContentChecked(){this._layoutTiles()}_checkCols(){this.cols}_checkRowHeight(){this._rowHeight||this._setTileStyler("1:1")}_setTileStyler(e){this._tileStyler&&this._tileStyler.reset(this),e===kn?this._tileStyler=new Jt:e&&e.indexOf(":")>-1?this._tileStyler=new Xt(e):this._tileStyler=new Wt(e)}_layoutTiles(){this._tileCoordinator||(this._tileCoordinator=new Rt);let e=this._tileCoordinator,i=this._tiles.filter(l=>!l._gridList||l._gridList===this),o=this._dir?this._dir.value:"ltr";this._tileCoordinator.update(this.cols,i),this._tileStyler.init(this.gutterSize,e,this.cols,o),i.forEach((l,m)=>{let w=e.positions[m];this._tileStyler.setStyle(l,w.row,w.col)}),this._setListStyle(this._tileStyler.getComputedHeight())}_setListStyle(e){e&&(this._element.nativeElement.style[e[0]]=e[1])}static \u0275fac=function(i){return new(i||n)};static \u0275cmp=O({type:n,selectors:[["mat-grid-list"]],contentQueries:function(i,o,l){if(i&1&&ui(l,Kt,5),i&2){let m;L(m=q())&&(o._tiles=m)}},hostAttrs:[1,"mat-grid-list"],hostVars:1,hostBindings:function(i,o){i&2&&he("cols",o.cols)},inputs:{cols:"cols",gutterSize:"gutterSize",rowHeight:"rowHeight"},exportAs:["matGridList"],features:[ot([{provide:un,useExisting:n}])],ngContentSelectors:pn,decls:2,vars:0,template:function(i,o){i&1&&(We(),r(0,"div"),Xe(1),a())},styles:[Mn],encapsulation:2,changeDetection:0})}return n})(),fn=(()=>{class n{static \u0275fac=function(i){return new(i||n)};static \u0275mod=at({type:n});static \u0275inj=it({imports:[jt,ye,jt,ye]})}return n})();function En(n,t){n&1&&(r(0,"div",7),v(1,"mat-spinner",8),r(2,"p"),s(3,"Loading available drivers..."),a()())}function Pn(n,t){if(n&1&&(r(0,"span",15),s(1),a()),n&2){let e=u().$implicit;d(),S(" (",e.rating.toFixed(1)," \u2B50) ")}}function Tn(n,t){if(n&1&&(r(0,"mat-option",13),s(1),p(2,Pn,2,1,"span",14),a()),n&2){let e=t.$implicit;c("value",e.id),d(),S(" ",e.full_name||e.email," "),d(),c("ngIf",e.rating)}}function In(n,t){n&1&&(r(0,"p",16),s(1," No approved drivers available at this time. "),a())}function An(n,t){if(n&1){let e=k();r(0,"div")(1,"mat-form-field",9)(2,"mat-label"),s(3,"Select Driver"),a(),r(4,"mat-select",10),ne("ngModelChange",function(o){g(e);let l=u();return ie(l.selectedDriverId,o)||(l.selectedDriverId=o),_(o)}),p(5,Tn,3,3,"mat-option",11),a()(),p(6,In,2,0,"p",12),a()}if(n&2){let e=u();d(4),te("ngModel",e.selectedDriverId),c("disabled",e.loading),d(),c("ngForOf",e.availableDrivers),d(),c("ngIf",e.availableDrivers.length===0)}}var Et=class n{constructor(t,e,i,o,l){this.dialogRef=t;this.data=e;this.userService=i;this.ratingService=o;this.snackBar=l}availableDrivers=[];selectedDriverId="";loading=!1;ngOnInit(){this.loadAvailableDrivers()}loadAvailableDrivers(){return M(this,null,function*(){this.loading=!0;try{let t=yield this.userService.getAllUsers();this.availableDrivers=t.filter(e=>e.role==="driver"&&e.is_approved===!0),yield Promise.all(this.availableDrivers.map(e=>M(this,null,function*(){try{let i=yield this.ratingService.getUserRatingSummary(e.id);i&&(e.rating=i.averageRating)}catch(i){console.error(`Error loading rating for driver ${e.id}:`,i)}}))),this.availableDrivers.sort((e,i)=>(i.rating||0)-(e.rating||0)),this.availableDrivers.length===0&&this.snackBar.open("No available drivers found","Close",{duration:3e3})}catch(t){console.error("Error loading drivers:",t),this.snackBar.open("Failed to load available drivers","Close",{duration:3e3})}finally{this.loading=!1}})}onCancel(){this.dialogRef.close()}onAssign(){this.selectedDriverId?this.availableDrivers.some(t=>t.id===this.selectedDriverId)?this.dialogRef.close(this.selectedDriverId):this.snackBar.open("Selected driver is no longer available","Close",{duration:3e3}):this.snackBar.open("Please select a driver","Close",{duration:3e3})}static \u0275fac=function(e){return new(e||n)(x(xe),x(ct),x(Z),x(Xi),x($))};static \u0275cmp=O({type:n,selectors:[["app-driver-selection-dialog"]],decls:10,vars:5,consts:[["mat-dialog-title",""],["mat-dialog-content",""],["class","loading-container",4,"ngIf"],[4,"ngIf"],["mat-dialog-actions","","align","end"],["mat-button","",3,"click","disabled"],["mat-raised-button","","color","primary",3,"click","disabled"],[1,"loading-container"],["diameter","40"],["appearance","outline",2,"width","100%"],[3,"ngModelChange","ngModel","disabled"],[3,"value",4,"ngFor","ngForOf"],["class","no-drivers-message",4,"ngIf"],[3,"value"],["class","driver-rating",4,"ngIf"],[1,"driver-rating"],[1,"no-drivers-message"]],template:function(e,i){e&1&&(r(0,"h2",0),s(1,"Assign Driver"),a(),r(2,"div",1),p(3,En,4,0,"div",2)(4,An,7,4,"div",3),a(),r(5,"div",4)(6,"button",5),f("click",function(){return i.onCancel()}),s(7,"Cancel"),a(),r(8,"button",6),f("click",function(){return i.onAssign()}),s(9),a()()),e&2&&(d(3),c("ngIf",i.loading),d(),c("ngIf",!i.loading),d(2),c("disabled",i.loading),d(2),c("disabled",!i.selectedDriverId||i.loading),d(),S(" ",i.loading?"Assigning...":"Assign"," "))},dependencies:[U,Ce,j,G,H,mt,ae,Me,ke,De,X,W,Q,le,Oe,Ae,N,z,ge,ue],styles:[".loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:10px;color:#0000008a}.no-drivers-message[_ngcontent-%COMP%]{color:#f44336;font-style:italic;text-align:center;margin:16px 0}.driver-rating[_ngcontent-%COMP%]{margin-left:8px;color:#0000008a}"]})};var Fn=["switch"],On=["*"];function Vn(n,t){n&1&&(r(0,"span",10),si(),r(1,"svg",12),v(2,"path",13),a(),r(3,"svg",14),v(4,"path",15),a()())}var Bn=new nt("mat-slide-toggle-default-options",{providedIn:"root",factory:()=>({disableToggleValue:!1,hideIcon:!1,disabledInteractive:!1})}),Un={provide:xi,useExisting:ri(()=>qe),multi:!0},Ft=class{source;checked;constructor(t,e){this.source=t,this.checked=e}},qe=(()=>{class n{_elementRef=B(Ge);_focusMonitor=B(fi);_changeDetectorRef=B(gi);defaults=B(Bn);_onChange=e=>{};_onTouched=()=>{};_validatorOnChange=()=>{};_uniqueId;_checked=!1;_createChangeEvent(e){return new Ft(this,e)}_labelId;get buttonId(){return`${this.id||this._uniqueId}-button`}_switchElement;focus(){this._switchElement.nativeElement.focus()}_noopAnimations;_focused;name=null;id;labelPosition="after";ariaLabel=null;ariaLabelledby=null;ariaDescribedby;required;color;disabled=!1;disableRipple=!1;tabIndex=0;get checked(){return this._checked}set checked(e){this._checked=e,this._changeDetectorRef.markForCheck()}hideIcon;disabledInteractive;change=new $t;toggleChange=new $t;get inputId(){return`${this.id||this._uniqueId}-input`}constructor(){B(hi).load(Si);let e=B(new li("tabindex"),{optional:!0}),i=this.defaults,o=B(di,{optional:!0});this.tabIndex=e==null?0:parseInt(e)||0,this.color=i.color||"accent",this._noopAnimations=o==="NoopAnimations",this.id=this._uniqueId=B(vi).getId("mat-mdc-slide-toggle-"),this.hideIcon=i.hideIcon??!1,this.disabledInteractive=i.disabledInteractive??!1,this._labelId=this._uniqueId+"-label"}ngAfterContentInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(e=>{e==="keyboard"||e==="program"?(this._focused=!0,this._changeDetectorRef.markForCheck()):e||Promise.resolve().then(()=>{this._focused=!1,this._onTouched(),this._changeDetectorRef.markForCheck()})})}ngOnChanges(e){e.required&&this._validatorOnChange()}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef)}writeValue(e){this.checked=!!e}registerOnChange(e){this._onChange=e}registerOnTouched(e){this._onTouched=e}validate(e){return this.required&&e.value!==!0?{required:!0}:null}registerOnValidatorChange(e){this._validatorOnChange=e}setDisabledState(e){this.disabled=e,this._changeDetectorRef.markForCheck()}toggle(){this.checked=!this.checked,this._onChange(this.checked)}_emitChangeEvent(){this._onChange(this.checked),this.change.emit(this._createChangeEvent(this.checked))}_handleClick(){this.disabled||(this.toggleChange.emit(),this.defaults.disableToggleValue||(this.checked=!this.checked,this._onChange(this.checked),this.change.emit(new Ft(this,this.checked))))}_getAriaLabelledBy(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:this._labelId}static \u0275fac=function(i){return new(i||n)};static \u0275cmp=O({type:n,selectors:[["mat-slide-toggle"]],viewQuery:function(i,o){if(i&1&&Y(Fn,5),i&2){let l;L(l=q())&&(o._switchElement=l.first)}},hostAttrs:[1,"mat-mdc-slide-toggle"],hostVars:13,hostBindings:function(i,o){i&2&&(pi("id",o.id),he("tabindex",null)("aria-label",null)("name",null)("aria-labelledby",null),ci(o.color?"mat-"+o.color:""),Qe("mat-mdc-slide-toggle-focused",o._focused)("mat-mdc-slide-toggle-checked",o.checked)("_mat-animation-noopable",o._noopAnimations))},inputs:{name:"name",id:"id",labelPosition:"labelPosition",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],required:[2,"required","required",ve],color:"color",disabled:[2,"disabled","disabled",ve],disableRipple:[2,"disableRipple","disableRipple",ve],tabIndex:[2,"tabIndex","tabIndex",e=>e==null?0:_i(e)],checked:[2,"checked","checked",ve],hideIcon:[2,"hideIcon","hideIcon",ve],disabledInteractive:[2,"disabledInteractive","disabledInteractive",ve]},outputs:{change:"change",toggleChange:"toggleChange"},exportAs:["matSlideToggle"],features:[ot([Un,{provide:Mi,useExisting:n,multi:!0}]),oi],ngContentSelectors:On,decls:13,vars:27,consts:[["switch",""],["mat-internal-form-field","",3,"labelPosition"],["role","switch","type","button",1,"mdc-switch",3,"click","tabIndex","disabled"],[1,"mdc-switch__track"],[1,"mdc-switch__handle-track"],[1,"mdc-switch__handle"],[1,"mdc-switch__shadow"],[1,"mdc-elevation-overlay"],[1,"mdc-switch__ripple"],["mat-ripple","",1,"mat-mdc-slide-toggle-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-switch__icons"],[1,"mdc-label",3,"click","for"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--on"],["d","M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--off"],["d","M20 13H4v-2h16v2z"]],template:function(i,o){if(i&1){let l=k();We(),r(0,"div",1)(1,"button",2,0),f("click",function(){return g(l),_(o._handleClick())}),v(3,"span",3),r(4,"span",4)(5,"span",5)(6,"span",6),v(7,"span",7),a(),r(8,"span",8),v(9,"span",9),a(),p(10,Vn,5,0,"span",10),a()()(),r(11,"label",11),f("click",function(w){return g(l),_(w.stopPropagation())}),Xe(12),a()()}if(i&2){let l=ee(2);c("labelPosition",o.labelPosition),d(),Qe("mdc-switch--selected",o.checked)("mdc-switch--unselected",!o.checked)("mdc-switch--checked",o.checked)("mdc-switch--disabled",o.disabled)("mat-mdc-slide-toggle-disabled-interactive",o.disabledInteractive),c("tabIndex",o.disabled&&!o.disabledInteractive?-1:o.tabIndex)("disabled",o.disabled&&!o.disabledInteractive),he("id",o.buttonId)("name",o.name)("aria-label",o.ariaLabel)("aria-labelledby",o._getAriaLabelledBy())("aria-describedby",o.ariaDescribedby)("aria-required",o.required||null)("aria-checked",o.checked)("aria-disabled",o.disabled&&o.disabledInteractive?"true":null),d(8),c("matRippleTrigger",l)("matRippleDisabled",o.disableRipple||o.disabled)("matRippleCentered",!0),d(),mi(o.hideIcon?-1:10),d(),c("for",o.buttonId),he("id",o._labelId)}},dependencies:[wi,Ri],styles:[`.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:"";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:""}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}
`],encapsulation:2,changeDetection:0})}return n})();var Ot=(()=>{class n{static \u0275fac=function(i){return new(i||n)};static \u0275mod=at({type:n});static \u0275inj=it({imports:[qe,ye,ye]})}return n})();var Vt=class n{constructor(t,e,i,o){this.dialogRef=t;this.data=e;this.userService=i;this.snackBar=o}formatRole(t){return t.charAt(0).toUpperCase()+t.slice(1)}formatDate(t){return new Date(t).toLocaleString()}toggleApprovalStatus(t){return M(this,null,function*(){try{if(t)if(yield this.userService.approveDriver(this.data.id))this.data.is_approved=!0,this.snackBar.open("User approved successfully","Close",{duration:3e3});else throw new Error("Failed to approve user");else if(yield this.userService.updateUserStatus(this.data.id,!1))this.data.is_approved=!1,this.snackBar.open("User approval revoked successfully","Close",{duration:3e3});else throw new Error("Failed to revoke user approval")}catch(e){console.error("Error updating user approval status:",e),this.snackBar.open("Failed to update user approval status","Close",{duration:3e3}),this.data.is_approved=!t}})}close(){this.dialogRef.close()}static \u0275fac=function(e){return new(e||n)(x(xe),x(ct),x(Z),x($))};static \u0275cmp=O({type:n,selectors:[["app-user-details-dialog"]],decls:38,vars:8,consts:[["mat-dialog-title",""],[1,"user-details"],[1,"detail-row"],[1,"label"],[1,"value"],["color","primary","selected",""],["selected","",3,"color"],["color","primary",3,"change","checked"],["align","end"],["mat-button","",3,"click"]],template:function(e,i){e&1&&(r(0,"h2",0),s(1,"User Details"),a(),r(2,"mat-dialog-content")(3,"div",1)(4,"div",2)(5,"span",3),s(6,"Email:"),a(),r(7,"span",4),s(8),a()(),r(9,"div",2)(10,"span",3),s(11,"Full Name:"),a(),r(12,"span",4),s(13),a()(),r(14,"div",2)(15,"span",3),s(16,"Phone:"),a(),r(17,"span",4),s(18),a()(),r(19,"div",2)(20,"span",3),s(21,"Role:"),a(),r(22,"mat-chip",5),s(23),a()(),r(24,"div",2)(25,"span",3),s(26,"Status:"),a(),r(27,"mat-chip",6),s(28),a(),r(29,"mat-slide-toggle",7),f("change",function(l){return i.toggleApprovalStatus(l.checked)}),a()(),r(30,"div",2)(31,"span",3),s(32,"Registered:"),a(),r(33,"span",4),s(34),a()()()(),r(35,"mat-dialog-actions",8)(36,"button",9),f("click",function(){return i.close()}),s(37,"Close"),a()()),e&2&&(d(8),D(i.data.email),d(5),D(i.data.full_name||"Not provided"),d(5),D(i.data.phone||"Not provided"),d(5),D(i.formatRole(i.data.role)),d(4),c("color",i.data.is_approved?"primary":"warn"),d(),S(" ",i.data.is_approved?"Approved":"Pending Approval"," "),d(),c("checked",i.data.is_approved),d(5),D(i.formatDate(i.data.created_at)))},dependencies:[U,G,ae,Me,ke,De,N,z,K,J,$e,_t,gt,Ot,qe],styles:[".user-details[_ngcontent-%COMP%]{padding:16px}.detail-row[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:16px}.label[_ngcontent-%COMP%]{font-weight:500;min-width:120px;color:#0000008a}.value[_ngcontent-%COMP%]{flex:1}mat-slide-toggle[_ngcontent-%COMP%]{margin-left:8px}"]})};var Nn=["cardElement"];function $n(n,t){n&1&&(r(0,"div",4)(1,"p"),s(2,"Loading Stripe SDK..."),a(),v(3,"mat-spinner",5),a())}function Ln(n,t){n&1&&(r(0,"mat-error"),s(1," Amount is required "),a())}function qn(n,t){n&1&&(r(0,"mat-error"),s(1," Amount must be at least $1 "),a())}function jn(n,t){if(n&1&&(r(0,"div",18),s(1),a()),n&2){let e=u(2);d(),D(e.cardError)}}function Hn(n,t){if(n&1){let e=k();r(0,"div",6)(1,"div",7)(2,"h3"),s(3,"Process a Payment"),a(),r(4,"p"),s(5,"Use this form to process a one-time payment"),a(),r(6,"form",8),f("ngSubmit",function(){g(e);let o=u();return _(o.processPayment())}),r(7,"div",9)(8,"mat-form-field",10)(9,"mat-label"),s(10,"Amount (USD)"),a(),v(11,"input",11),p(12,Ln,2,0,"mat-error",12)(13,qn,2,0,"mat-error",12),a()(),r(14,"div",9)(15,"mat-form-field",10)(16,"mat-label"),s(17,"Description"),a(),v(18,"input",13),a()(),r(19,"div",9),v(20,"div",14,0),p(22,jn,2,1,"div",15),a(),r(23,"div",16)(24,"button",17)(25,"mat-icon"),s(26,"payment"),a(),s(27),a()()()()()}if(n&2){let e,i,o=u();d(6),c("formGroup",o.paymentForm),d(6),c("ngIf",(e=o.paymentForm.get("amount"))==null?null:e.hasError("required")),d(),c("ngIf",(i=o.paymentForm.get("amount"))==null?null:i.hasError("min")),d(9),c("ngIf",o.cardError),d(2),c("disabled",o.paymentForm.invalid||o.processing),d(3),S(" ",o.processing?"Processing...":"Process Payment"," ")}}var Bt=class n{constructor(t,e,i,o,l){this.formBuilder=t;this.snackBar=e;this.paymentService=i;this.rideService=o;this.authService=l;this.paymentForm=this.formBuilder.group({amount:[10,[F.required,F.min(1)]],description:["",F.required]})}cardElement;paymentForm;stripe;card;sdkLoaded=!1;processing=!1;cardError="";paymentResult=null;recentTransactions=[];ngOnInit(){return M(this,null,function*(){yield this.loadStripe()})}ngAfterViewInit(){this.sdkLoaded&&this.initializeCard()}loadStripe(){return M(this,null,function*(){try{let t=yield Hi(Ht.stripePublishableKey);this.stripe=t,this.sdkLoaded=!0,setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error loading Stripe:",t),this.snackBar.open("Error loading Stripe. Please check your API keys.","Close",{duration:5e3})}})}loadStripeScript(){if(window.Stripe){this.initializeStripe();return}let t=document.createElement("script");t.src="https://js.stripe.com/v3/",t.async=!0,t.onload=()=>{this.initializeStripe()},document.body.appendChild(t)}initializeStripe(){if(!window.Stripe){this.snackBar.open("Stripe SDK not available","Close",{duration:3e3});return}try{this.stripe=window.Stripe(Ht.stripePublishableKey),setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error initializing Stripe:",t),this.snackBar.open("Error initializing Stripe payments. Check your credentials.","Close",{duration:5e3})}}initializeCard(){if(!this.cardElement||!this.cardElement.nativeElement||!this.stripe){setTimeout(()=>this.initializeCard(),100);return}try{let t=this.stripe.elements();this.card=t.create("card",{style:{base:{iconColor:"#666EE8",color:"#31325F",fontWeight:400,fontFamily:'"Helvetica Neue", Helvetica, sans-serif',fontSize:"16px","::placeholder":{color:"#CFD7E0"}}}}),this.card.mount(this.cardElement.nativeElement),this.card.on("change",e=>{this.cardError=e.error?e.error.message:""}),this.sdkLoaded=!0}catch(t){console.error("Error initializing Stripe card:",t),this.snackBar.open("Error initializing Stripe card form","Close",{duration:5e3})}}loadRecentTransactions(){return M(this,null,function*(){try{let{data:t,error:e}=yield this.authService.supabase.functions.invoke("stripe",{body:{action:"listPaymentIntents",limit:10}});if(e){console.error("Error loading recent transactions:",e),this.snackBar.open("Error loading recent transactions","Close",{duration:3e3});return}t&&t.paymentIntents&&(this.recentTransactions=t.paymentIntents.map(i=>({id:i.id,date:new Date(i.created*1e3),amount:i.amount/100,description:i.description||"No description",status:this.formatPaymentStatus(i.status)})))}catch(t){console.error("Error loading recent transactions:",t),this.snackBar.open("Error loading recent transactions","Close",{duration:3e3})}})}formatPaymentStatus(t){return t.charAt(0).toUpperCase()+t.slice(1)}processPayment(){return M(this,null,function*(){if(this.paymentForm.invalid||!this.card||!this.stripe)return;this.processing=!0,this.paymentResult=null;let t=this.paymentForm.get("amount")?.value,e=this.paymentForm.get("description")?.value;try{let{paymentMethod:i,error:o}=yield this.stripe.createPaymentMethod({type:"card",card:this.card});if(o)throw o;let l={amount:t*100,currency:"usd",description:e,payment_method:i.id};console.log(l);let{data:m,error:w}=yield this.authService.supabase.functions.invoke("stripe",{body:l});if(w)throw console.error("Error creating payment intent:",w),new Error(`Failed to create payment intent: ${w.message}`);if(console.log("Payment intent created:",m),!m||!m.client_secret)throw new Error("No client secret returned from payment intent creation");let y=m.client_secret,{error:R,paymentIntent:A}=yield this.stripe.confirmCardPayment(y,{payment_method:i.id});if(R)throw R;this.paymentResult={success:!0,paymentIntent:A},this.snackBar.open("Payment processed successfully!","Close",{duration:3e3}),this.paymentForm.reset({amount:10}),this.card.clear()}catch(i){console.error("Error processing payment:",i),this.paymentResult={success:!1,error:{message:i.message}},this.snackBar.open(`Payment error: ${i.message}`,"Close",{duration:5e3})}finally{this.processing=!1}})}static \u0275fac=function(e){return new(e||n)(x(Ie),x($),x(Ne),x(pe),x(Oi))};static \u0275cmp=O({type:n,selectors:[["app-stripe-payment"]],viewQuery:function(e,i){if(e&1&&Y(Nn,5),e&2){let o;L(o=q())&&(i.cardElement=o.first)}},decls:10,vars:2,consts:[["cardElement",""],[1,"container"],["class","sdk-status",4,"ngIf"],["class","payment-tabs",4,"ngIf"],[1,"sdk-status"],["diameter","30"],[1,"payment-tabs"],[1,"payment-section"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline"],["matInput","","type","number","formControlName","amount","min","1","step","0.01"],[4,"ngIf"],["matInput","","formControlName","description","placeholder","Payment description"],[1,"card-element"],["class","card-errors",4,"ngIf"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"card-errors"]],template:function(e,i){e&1&&(r(0,"div",1)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),s(4,"Stripe Payment Processing"),a(),r(5,"mat-card-subtitle"),s(6,"Process payments securely with Stripe"),a()(),r(7,"mat-card-content"),p(8,$n,4,0,"div",2)(9,Hn,28,6,"div",3),a()()()),e&2&&(d(8),c("ngIf",!i.sdkLoaded),d(),c("ngIf",i.sdkLoaded))},dependencies:[U,j,G,Ee,oe,pt,H,Re,Di,se,Pe,Te,K,Ve,Ue,ze,Vi,Be,X,W,Q,Fe,me,ce,N,z,le,re,ge,ue,$e,J,de,Le],styles:[".container[_ngcontent-%COMP%]{padding:20px}.form-row[_ngcontent-%COMP%]{margin-bottom:20px}mat-form-field[_ngcontent-%COMP%]{width:100%}.card-element[_ngcontent-%COMP%]{border:1px solid #ccc;padding:10px;border-radius:4px;height:40px;background-color:#fff}.card-errors[_ngcontent-%COMP%]{color:#f44336;margin-top:8px;font-size:14px}.form-actions[_ngcontent-%COMP%]{margin-top:20px;margin-bottom:20px}.divider[_ngcontent-%COMP%]{margin:30px 0}.payment-result[_ngcontent-%COMP%]{margin-top:20px}.payment-result[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:10px;border-radius:4px;overflow-x:auto}.payment-section[_ngcontent-%COMP%]{margin-bottom:30px}.payment-tabs[_ngcontent-%COMP%]{margin-top:20px}.recent-transactions[_ngcontent-%COMP%]{margin-top:30px}table[_ngcontent-%COMP%]{width:100%}.status-succeeded[_ngcontent-%COMP%], .status-completed[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.status-pending[_ngcontent-%COMP%]{color:#ff9800;font-weight:500}.status-failed[_ngcontent-%COMP%], .status-refunded[_ngcontent-%COMP%]{color:#f44336;font-weight:500}.result-card[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;border-radius:4px;margin-top:10px}.result-card.success[_ngcontent-%COMP%]{background-color:#4caf501a}.result-card.error[_ngcontent-%COMP%]{background-color:#f443361a}.result-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:15px;font-size:24px;height:24px;width:24px}.result-card.success[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#4caf50}.result-card.error[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f44336}.result-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px}.result-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.sdk-status[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;padding:40px}"]})};function Wn(n,t){n&1&&(r(0,"mat-error"),s(1," Name is required "),a())}function Xn(n,t){n&1&&(r(0,"mat-error"),s(1," Base fare is required "),a())}function Jn(n,t){n&1&&(r(0,"mat-error"),s(1," Base fare must be positive "),a())}function Kn(n,t){n&1&&(r(0,"mat-error"),s(1," Distance rate is required "),a())}function Zn(n,t){n&1&&(r(0,"mat-error"),s(1," Distance rate must be positive "),a())}function Yn(n,t){n&1&&(r(0,"mat-error"),s(1," Time rate is required "),a())}function er(n,t){n&1&&(r(0,"mat-error"),s(1," Time rate must be positive "),a())}function tr(n,t){n&1&&(r(0,"div",20),v(1,"mat-spinner",21),r(2,"p"),s(3,"Loading pricing configurations..."),a()())}function ir(n,t){n&1&&(r(0,"th",35),s(1,"Name"),a())}function nr(n,t){if(n&1&&(r(0,"td",36),s(1),a()),n&2){let e=t.$implicit;d(),D(e.name)}}function rr(n,t){n&1&&(r(0,"th",35),s(1,"Base Fare"),a())}function ar(n,t){if(n&1&&(r(0,"td",36),s(1),a()),n&2){let e=t.$implicit;d(),S("$",e.base_fare.toFixed(2),"")}}function or(n,t){n&1&&(r(0,"th",35),s(1,"Distance Rate"),a())}function sr(n,t){if(n&1&&(r(0,"td",36),s(1),a()),n&2){let e=t.$implicit;d(),S("$",e.distance_rate.toFixed(2),"/mile")}}function lr(n,t){n&1&&(r(0,"th",35),s(1,"Time Rate"),a())}function dr(n,t){if(n&1&&(r(0,"td",36),s(1),a()),n&2){let e=t.$implicit;d(),S("$",e.time_rate.toFixed(2),"/min")}}function cr(n,t){n&1&&(r(0,"th",35),s(1,"Status"),a())}function mr(n,t){if(n&1&&(r(0,"td",36)(1,"span",37),s(2),a()()),n&2){let e=t.$implicit;d(),Qe("active",e.is_active),d(),S(" ",e.is_active?"Active":"Inactive"," ")}}function pr(n,t){n&1&&(r(0,"th",35),s(1,"Actions"),a())}function ur(n,t){if(n&1){let e=k();r(0,"td",36)(1,"button",38),f("click",function(){let o=g(e).$implicit,l=u(2);return _(l.editPricing(o))}),r(2,"mat-icon"),s(3,"edit"),a()(),r(4,"button",39),f("click",function(){let o=g(e).$implicit,l=u(2);return _(l.toggleActive(o))}),r(5,"mat-icon"),s(6),a()(),r(7,"button",40),f("click",function(){let o=g(e).$implicit,l=u(2);return _(l.deletePricing(o.id))}),r(8,"mat-icon"),s(9,"delete"),a()()()}if(n&2){let e=t.$implicit;d(4),c("matTooltip",e.is_active?"Deactivate":"Activate"),d(2),D(e.is_active?"toggle_on":"toggle_off"),d(),c("disabled",e.is_active)}}function gr(n,t){n&1&&v(0,"tr",41)}function _r(n,t){n&1&&v(0,"tr",42)}function fr(n,t){n&1&&(r(0,"div",43)(1,"p"),s(2,"No pricing configurations found. Create one to get started."),a()())}function hr(n,t){if(n&1&&(r(0,"div",22)(1,"table",23),T(2,24),p(3,ir,2,0,"th",25)(4,nr,2,1,"td",26),I(),T(5,27),p(6,rr,2,0,"th",25)(7,ar,2,1,"td",26),I(),T(8,28),p(9,or,2,0,"th",25)(10,sr,2,1,"td",26),I(),T(11,29),p(12,lr,2,0,"th",25)(13,dr,2,1,"td",26),I(),T(14,30),p(15,cr,2,0,"th",25)(16,mr,3,3,"td",26),I(),T(17,31),p(18,pr,2,0,"th",25)(19,ur,10,3,"td",26),I(),p(20,gr,1,0,"tr",32)(21,_r,1,0,"tr",33),a(),p(22,fr,3,0,"div",34),a()),n&2){let e=u();d(),c("dataSource",e.pricingConfigurations),d(19),c("matHeaderRowDef",e.displayedColumns),d(),c("matRowDefColumns",e.displayedColumns),d(),c("ngIf",e.pricingConfigurations.length===0)}}var Ut=class n{constructor(t,e,i){this.fb=t;this.ridePricingService=e;this.snackBar=i;this.pricingForm=this.fb.group({name:["",[F.required]],base_fare:[5,[F.required,F.min(0)]],distance_rate:[1.5,[F.required,F.min(0)]],time_rate:[.25,[F.required,F.min(0)]],is_active:[!0]})}pricingForm;pricingConfigurations=[];displayedColumns=["name","base_fare","distance_rate","time_rate","is_active","actions"];loading=!1;activePricing=null;editMode=!1;editingId=null;ngOnInit(){this.loadPricingConfigurations()}loadPricingConfigurations(){return M(this,null,function*(){this.loading=!0;try{this.pricingConfigurations=yield this.ridePricingService.getAllPricing(),this.activePricing=yield this.ridePricingService.loadActivePricing()}catch(t){console.error("Error loading pricing configurations:",t),this.snackBar.open("Failed to load pricing configurations","Close",{duration:3e3})}finally{this.loading=!1}})}onSubmit(){return M(this,null,function*(){if(!this.pricingForm.invalid){this.loading=!0;try{let t=this.pricingForm.value;this.editMode&&this.editingId?(yield this.ridePricingService.updatePricing(this.editingId,t),this.snackBar.open("Pricing configuration updated successfully","Close",{duration:3e3})):(yield this.ridePricingService.createPricing(t),this.snackBar.open("Pricing configuration created successfully","Close",{duration:3e3})),this.resetForm(),yield this.loadPricingConfigurations()}catch(t){console.error("Error saving pricing configuration:",t),this.snackBar.open("Failed to save pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}}})}editPricing(t){this.editMode=!0,this.editingId=t.id,this.pricingForm.patchValue({name:t.name,base_fare:t.base_fare,distance_rate:t.distance_rate,time_rate:t.time_rate,is_active:t.is_active})}resetForm(){this.editMode=!1,this.editingId=null,this.pricingForm.reset({name:"",base_fare:5,distance_rate:1.5,time_rate:.25,is_active:!0})}toggleActive(t){return M(this,null,function*(){this.loading=!0;try{yield this.ridePricingService.setActiveStatus(t.id,!t.is_active),yield this.loadPricingConfigurations(),this.snackBar.open(`Pricing configuration ${t.is_active?"deactivated":"activated"} successfully`,"Close",{duration:3e3})}catch(e){console.error("Error toggling active status:",e),this.snackBar.open("Failed to update pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}})}deletePricing(t){return M(this,null,function*(){if(confirm("Are you sure you want to delete this pricing configuration?")){this.loading=!0;try{yield this.ridePricingService.deletePricing(t),yield this.loadPricingConfigurations(),this.snackBar.open("Pricing configuration deleted successfully","Close",{duration:3e3})}catch(e){console.error("Error deleting pricing configuration:",e),this.snackBar.open("Failed to delete pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}}})}calculateSampleFare(){let t=this.pricingForm.get("base_fare")?.value||0,e=this.pricingForm.get("distance_rate")?.value||0,i=this.pricingForm.get("time_rate")?.value||0;return+(t+10*e+20*i).toFixed(2)}static \u0275fac=function(e){return new(e||n)(x(Ie),x(qi),x($))};static \u0275cmp=O({type:n,selectors:[["app-ride-pricing"]],decls:57,vars:15,consts:[[1,"pricing-container"],[1,"form-card"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline",1,"full-width"],["matInput","","formControlName","name","placeholder","e.g., Standard, Premium, etc."],[4,"ngIf"],["appearance","outline"],["matInput","","type","number","step","0.01","formControlName","base_fare"],["matInput","","type","number","step","0.01","formControlName","distance_rate"],["matInput","","type","number","step","0.01","formControlName","time_rate"],[1,"active-toggle"],["formControlName","is_active","color","primary"],[1,"sample-calculation"],[1,"form-actions"],["mat-button","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"table-card"],["class","loading-container",4,"ngIf"],["class","table-container",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"table-container"],["mat-table","",1,"mat-elevation-z2",3,"dataSource"],["matColumnDef","name"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","base_fare"],["matColumnDef","distance_rate"],["matColumnDef","time_rate"],["matColumnDef","is_active"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["class","no-data",4,"ngIf"],["mat-header-cell",""],["mat-cell",""],[1,"status-badge"],["mat-icon-button","","color","primary","matTooltip","Edit",3,"click"],["mat-icon-button","","color","accent",3,"click","matTooltip"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click","disabled"],["mat-header-row",""],["mat-row",""],[1,"no-data"]],template:function(e,i){if(e&1&&(r(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),s(4),a()(),r(5,"mat-card-content")(6,"form",2),f("ngSubmit",function(){return i.onSubmit()}),r(7,"div",3)(8,"mat-form-field",4)(9,"mat-label"),s(10,"Configuration Name"),a(),v(11,"input",5),p(12,Wn,2,0,"mat-error",6),a()(),r(13,"div",3)(14,"mat-form-field",7)(15,"mat-label"),s(16,"Base Fare ($)"),a(),v(17,"input",8),p(18,Xn,2,0,"mat-error",6)(19,Jn,2,0,"mat-error",6),a(),r(20,"mat-form-field",7)(21,"mat-label"),s(22,"Distance Rate ($ per mile)"),a(),v(23,"input",9),p(24,Kn,2,0,"mat-error",6)(25,Zn,2,0,"mat-error",6),a(),r(26,"mat-form-field",7)(27,"mat-label"),s(28,"Time Rate ($ per minute)"),a(),v(29,"input",10),p(30,Yn,2,0,"mat-error",6)(31,er,2,0,"mat-error",6),a()(),r(32,"div",3)(33,"div",11)(34,"mat-slide-toggle",12),s(35," Set as Active Configuration "),a()()(),r(36,"div",13),v(37,"mat-divider"),r(38,"h3"),s(39,"Sample Fare Calculation"),a(),r(40,"p"),s(41,"For a 10-mile, 20-minute ride: "),r(42,"strong"),s(43),a()(),v(44,"mat-divider"),a(),r(45,"div",14)(46,"button",15),f("click",function(){return i.resetForm()}),s(47),a(),r(48,"button",16),s(49),a()()()()(),r(50,"mat-card",17)(51,"mat-card-header")(52,"mat-card-title"),s(53,"Pricing Configurations"),a()(),r(54,"mat-card-content"),p(55,tr,4,0,"div",18)(56,hr,23,4,"div",19),a()()()),e&2){let o,l,m,w,y,R,A;d(4),D(i.editMode?"Edit Pricing Configuration":"Create New Pricing Configuration"),d(2),c("formGroup",i.pricingForm),d(6),c("ngIf",(o=i.pricingForm.get("name"))==null?null:o.hasError("required")),d(6),c("ngIf",(l=i.pricingForm.get("base_fare"))==null?null:l.hasError("required")),d(),c("ngIf",(m=i.pricingForm.get("base_fare"))==null?null:m.hasError("min")),d(5),c("ngIf",(w=i.pricingForm.get("distance_rate"))==null?null:w.hasError("required")),d(),c("ngIf",(y=i.pricingForm.get("distance_rate"))==null?null:y.hasError("min")),d(5),c("ngIf",(R=i.pricingForm.get("time_rate"))==null?null:R.hasError("required")),d(),c("ngIf",(A=i.pricingForm.get("time_rate"))==null?null:A.hasError("min")),d(12),S("$",i.calculateSampleFare(),""),d(4),S(" ",i.editMode?"Cancel":"Reset"," "),d(),c("disabled",i.pricingForm.invalid||i.loading),d(),S(" ",i.editMode?"Update":"Create"," "),d(6),c("ngIf",i.loading),d(),c("ngIf",!i.loading)}},dependencies:[U,j,G,Ee,oe,pt,H,Re,se,Pe,Te,K,Ve,Ue,ze,Be,X,W,Q,Fe,me,ce,N,z,dt,J,de,Le,ft,vt,Ct,wt,ht,yt,St,bt,xt,Mt,re,ge,ue,Ot,qe,kt,Dt,$e,ji],styles:[".pricing-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;max-width:1200px;margin:0 auto}.form-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]{width:100%}.form-row[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-bottom:16px;align-items:center}.full-width[_ngcontent-%COMP%]{width:100%}mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:150px}.active-toggle[_ngcontent-%COMP%]{margin:16px 0}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:8px;margin-top:16px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}table[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#0000008a}.no-data[_ngcontent-%COMP%]{text-align:center;padding:20px;color:#0000008a}.status-badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500;background-color:#e0e0e0;color:#757575}.status-badge.active[_ngcontent-%COMP%]{background-color:#c8e6c9;color:#2e7d32}.sample-calculation[_ngcontent-%COMP%]{margin:20px 0;padding:10px 0}.sample-calculation[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:10px 0;font-size:16px;font-weight:500}.sample-calculation[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:10px 0}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{flex-direction:column}mat-form-field[_ngcontent-%COMP%]{width:100%}}"]})};function vr(n,t){if(n&1&&(r(0,"mat-option",21),s(1),a()),n&2){let e=t.$implicit;c("value",e.id),d(),S(" ",e.full_name||e.email," ")}}function wr(n,t){n&1&&(r(0,"mat-error"),s(1," Rider is required "),a())}function Sr(n,t){n&1&&(r(0,"mat-error"),s(1," Pickup location is required "),a())}function br(n,t){n&1&&(r(0,"mat-error"),s(1," Dropoff location is required "),a())}function Cr(n,t){if(n&1&&(r(0,"p"),s(1),a()),n&2){let e=u(3);d(),S("Distance: ",e.estimatedDistance," miles")}}function yr(n,t){if(n&1&&(r(0,"p"),s(1),a()),n&2){let e=u(3);d(),S("Duration: ",e.estimatedDuration," minutes")}}function xr(n,t){if(n&1&&(r(0,"div",24)(1,"p"),s(2,"Estimated fare: "),r(3,"strong"),s(4),a()(),p(5,Cr,2,1,"p",7)(6,yr,2,1,"p",7),a()),n&2){let e=u(2);d(4),D(e.estimatedFare?"$"+e.estimatedFare.toFixed(2):""),d(),c("ngIf",e.estimatedDistance),d(),c("ngIf",e.estimatedDuration)}}function Mr(n,t){if(n&1&&(r(0,"div"),v(1,"app-map-display",22),p(2,xr,7,3,"div",23),a()),n&2){let e,i,o=u();d(),c("origin",(e=o.rideForm.get("pickup_location"))==null?null:e.value)("destination",(i=o.rideForm.get("dropoff_location"))==null?null:i.value),d(),c("ngIf",o.estimatedFare)}}function Dr(n,t){n&1&&(r(0,"mat-error"),s(1," Pickup date is required "),a())}function kr(n,t){n&1&&(r(0,"mat-error"),s(1," Pickup time is required "),a())}var zt=class n{constructor(t,e,i,o,l,m,w){this.formBuilder=t;this.dialogRef=e;this.rideService=i;this.userService=o;this.locationService=l;this.paymentService=m;this.snackBar=w;this.rideForm=this.formBuilder.group({rider_id:["",F.required],pickup_location:["",F.required],dropoff_location:["",F.required],pickup_date:[new Date,F.required],pickup_time:["12:00 PM",F.required]})}rideForm;riders=[];loading=!1;showMap=!1;estimatedFare=null;estimatedDistance=null;estimatedDuration=null;locationCoordinates={};ngOnInit(){return M(this,null,function*(){try{let t=yield this.userService.getUsersByRole("rider");this.riders=t}catch(t){console.error("Error loading riders:",t),this.snackBar.open("Failed to load riders","Close",{duration:3e3})}this.rideForm.get("pickup_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()}),this.rideForm.get("dropoff_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()})})}updateRouteEstimates(){return M(this,null,function*(){let t=this.rideForm.get("pickup_location")?.value,e=this.rideForm.get("dropoff_location")?.value;if(t&&e){this.showMap=!0;try{let{fare:i,routeInfo:o}=yield this.paymentService.estimateFare(t,e);this.estimatedFare=i,this.estimatedDistance=o.distance,this.estimatedDuration=o.duration}catch(i){console.error("Error calculating route:",i)}}else this.showMap=!1,this.estimatedFare=null,this.estimatedDistance=null,this.estimatedDuration=null})}onSubmit(){return M(this,null,function*(){if(!this.rideForm.invalid){this.loading=!0;try{this.locationCoordinates.pickup||(this.locationCoordinates.pickup=yield this.locationService.geocodeAddress(this.rideForm.value.pickup_location)),this.locationCoordinates.dropoff||(this.locationCoordinates.dropoff=yield this.locationService.geocodeAddress(this.rideForm.value.dropoff_location));let t=yield this.locationService.calculateRoute(this.locationCoordinates.pickup,this.locationCoordinates.dropoff),e=this.rideForm.value.pickup_date,i=this.rideForm.value.pickup_time,o=new Date(e),l=i.match(/(\d+):(\d+)\s?(AM|PM)?/i);if(l){let y=parseInt(l[1],10),R=parseInt(l[2],10),A=l[3]?l[3].toUpperCase():null;A==="PM"&&y<12?y+=12:A==="AM"&&y===12&&(y=0),o.setHours(y,R,0,0)}let m=He(je({},this.rideForm.value),{status:"requested",pickup_time:o.toISOString(),pickup_latitude:this.locationCoordinates.pickup?.latitude,pickup_longitude:this.locationCoordinates.pickup?.longitude,dropoff_latitude:this.locationCoordinates.dropoff?.latitude,dropoff_longitude:this.locationCoordinates.dropoff?.longitude,distance_miles:t.distance,duration_minutes:t.duration,fare:this.estimatedFare||(yield this.paymentService.estimateFare(this.rideForm.value.pickup_location,this.rideForm.value.dropoff_location)).fare}),w=yield this.rideService.createRide(m);this.snackBar.open("Ride created successfully!","Close",{duration:3e3}),this.dialogRef.close(w)}catch(t){console.error("Error creating ride:",t),this.snackBar.open(t.message||"Failed to create ride","Close",{duration:3e3})}finally{this.loading=!1}}})}static \u0275fac=function(e){return new(e||n)(x(Ie),x(xe),x(pe),x(Z),x($i),x(Ne),x($))};static \u0275cmp=O({type:n,selectors:[["app-admin-ride-create-dialog"]],decls:50,vars:14,consts:[["picker",""],["timepicker",""],["mat-dialog-title",""],[3,"formGroup"],["appearance","outline",1,"full-width"],["formControlName","rider_id","required",""],[3,"value",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"location-fields"],["matInput","","formControlName","pickup_location","placeholder","Enter pickup location"],["matInput","","formControlName","dropoff_location","placeholder","Enter dropoff location"],[1,"date-time-fields"],["appearance","outline"],["matInput","","formControlName","pickup_date",3,"matDatepicker"],["matSuffix","",3,"for"],["matInput","","formControlName","pickup_time",3,"ngxMatTimepicker"],["ngxMatTimepickerToggleIcon",""],["matInput","","formControlName","notes","placeholder","Any special requirements?"],["align","end"],["mat-button","","mat-dialog-close",""],["mat-raised-button","","color","primary",3,"click","disabled"],[3,"value"],[3,"origin","destination"],["class","fare-estimate",4,"ngIf"],[1,"fare-estimate"]],template:function(e,i){if(e&1){let o=k();r(0,"h2",2),s(1,"Create Ride for User"),a(),r(2,"mat-dialog-content")(3,"form",3)(4,"mat-form-field",4)(5,"mat-label"),s(6,"Select Rider"),a(),r(7,"mat-select",5),p(8,vr,2,2,"mat-option",6),a(),p(9,wr,2,0,"mat-error",7),a(),r(10,"div",8)(11,"mat-form-field",4)(12,"mat-label"),s(13,"Pickup Location"),a(),v(14,"input",9),p(15,Sr,2,0,"mat-error",7),a(),r(16,"mat-form-field",4)(17,"mat-label"),s(18,"Dropoff Location"),a(),v(19,"input",10),p(20,br,2,0,"mat-error",7),a()(),p(21,Mr,3,3,"div",7),r(22,"div",11)(23,"mat-form-field",12)(24,"mat-label"),s(25,"Pickup Date"),a(),v(26,"input",13)(27,"mat-datepicker-toggle",14)(28,"mat-datepicker",null,0),p(30,Dr,2,0,"mat-error",7),a(),r(31,"mat-form-field",12)(32,"mat-label"),s(33,"Pickup Time"),a(),v(34,"input",15),r(35,"ngx-mat-timepicker-toggle",14)(36,"mat-icon",16),s(37,"keyboard_arrow_down"),a()(),v(38,"ngx-mat-timepicker",null,1),p(40,kr,2,0,"mat-error",7),a()(),r(41,"mat-form-field",4)(42,"mat-label"),s(43,"Special Notes"),a(),v(44,"textarea",17),a()()(),r(45,"mat-dialog-actions",18)(46,"button",19),s(47,"Cancel"),a(),r(48,"button",20),f("click",function(){return g(o),_(i.onSubmit())}),s(49),a()()}if(e&2){let o,l,m,w,y,R,A=ee(29),V=ee(39);d(3),c("formGroup",i.rideForm),d(5),c("ngForOf",i.riders),d(),c("ngIf",(o=i.rideForm.get("rider_id"))==null||o.errors==null?null:o.errors.required),d(6),c("ngIf",(l=i.rideForm.get("pickup_location"))==null||l.errors==null?null:l.errors.required),d(5),c("ngIf",(m=i.rideForm.get("dropoff_location"))==null||m.errors==null?null:m.errors.required),d(),c("ngIf",i.showMap&&((w=i.rideForm.get("pickup_location"))==null?null:w.value)&&((w=i.rideForm.get("dropoff_location"))==null?null:w.value)),d(5),c("matDatepicker",A),d(),c("for",A),d(3),c("ngIf",(y=i.rideForm.get("pickup_date"))==null||y.errors==null?null:y.errors.required),d(4),c("ngxMatTimepicker",V),d(),c("for",V),d(5),c("ngIf",(R=i.rideForm.get("pickup_time"))==null||R.errors==null?null:R.errors.required),d(8),c("disabled",i.rideForm.invalid||i.loading),d(),S(" ",i.loading?"Creating...":"Create Ride"," ")}},dependencies:[U,Ce,j,se,Ee,oe,H,Re,ki,Pe,Te,ae,yi,Me,ke,De,K,X,W,Q,Fe,ut,me,ce,N,z,Ni,Bi,Ui,zi,Ei,J,de,le,Oe,Ae,re,Fi,Pi,Ii,Ai,Ti,Li],styles:[".full-width[_ngcontent-%COMP%]{width:100%}form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;min-width:500px}.location-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.date-time-fields[_ngcontent-%COMP%]{display:flex;gap:16px}textarea[_ngcontent-%COMP%]{min-height:80px}.fare-estimate[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:4px;margin-top:16px;margin-bottom:16px}.fare-estimate[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}"]})};var Nt=class n{constructor(t,e,i){this.userService=t;this.rideService=e;this.paymentService=i}statisticsSubject=new ni(null);statistics$=this.statisticsSubject.asObservable();generateSystemStatistics(){return M(this,null,function*(){let t=yield this.userService.getAllUsers(),e=t.filter(E=>E.role==="rider"),i=t.filter(E=>E.role==="driver"),o=t.filter(E=>E.role==="admin"),l=yield this.rideService.getAllRides(),m=l.filter(E=>E.status==="completed"),w=l.filter(E=>E.status==="in-progress"),y=l.filter(E=>E.status==="requested"),R=l.filter(E=>E.status==="canceled"),A=[{id:"1",type:"user_registered",timestamp:new Date().toISOString(),details:{userId:"user1",role:"rider"}},{id:"2",type:"ride_requested",timestamp:new Date(Date.now()-36e5).toISOString(),details:{rideId:"ride1",riderId:"rider1"}},{id:"3",type:"ride_completed",timestamp:new Date(Date.now()-72e5).toISOString(),details:{rideId:"ride2",riderId:"rider2",driverId:"driver1"}},{id:"4",type:"driver_approved",timestamp:new Date(Date.now()-108e5).toISOString(),details:{driverId:"driver2"}}],V={totalUsers:{all:t.length,riders:e.length,drivers:i.length,admins:o.length},rides:{total:l.length,completed:m.length,inProgress:w.length,requested:y.length,canceled:R.length},recentActivity:A};return this.statisticsSubject.next(V),V})}generateRideReport(t){return M(this,null,function*(){let{dateRange:e}=t,o=(yield this.rideService.getAllRides()).filter(h=>{let b=new Date(h.created_at);return b>=e.startDate&&b<=e.endDate}),l=o.filter(h=>h.status==="requested").length,m=o.filter(h=>h.status==="assigned").length,w=o.filter(h=>h.status==="in-progress").length,y=o.filter(h=>h.status==="completed").length,R=o.filter(h=>h.status==="canceled").length,A=this.groupRidesByDay(o),V=o.filter(h=>h.status==="completed"&&h.duration_minutes&&h.distance_miles),E=V.length>0?V.reduce((h,b)=>h+(b.duration_minutes||0),0)/V.length:0,Se=V.length>0?V.reduce((h,b)=>h+(b.distance_miles||0),0)/V.length:0;return{dateRange:e,ridesByStatus:{requested:l,assigned:m,inProgress:w,completed:y,canceled:R},ridesByDay:A,averageDuration:E,averageDistance:Se}})}generateRevenueReport(t){return M(this,null,function*(){let{dateRange:e}=t,i=yield this.rideService.getAllRides(),o=yield this.userService.getAllUsers(),l=i.filter(h=>{let b=new Date(h.created_at);return b>=e.startDate&&b<=e.endDate}),m=l.reduce((h,b)=>h+(b.amount||b.fare||0),0),w=this.groupRevenueByDay(l),y=l.filter(h=>h.payment_status==="pending").reduce((h,b)=>h+(b.amount||b.fare||0),0),R=l.filter(h=>h.payment_status==="paid").reduce((h,b)=>h+(b.amount||b.fare||0),0),A=l.filter(h=>h.payment_status==="failed").reduce((h,b)=>h+(b.amount||b.fare||0),0),V=l.filter(h=>h.payment_status==="refunded").reduce((h,b)=>h+(b.amount||b.fare||0),0),E=new Map;l.filter(h=>h.driver_id&&(h.payment_status==="paid"||h.status==="completed")).forEach(h=>{let b=h.driver_id,be=h.amount||h.fare||0;if(E.has(b)){let C=E.get(b);E.set(b,{revenue:C.revenue+be,rideCount:C.rideCount+1})}else E.set(b,{revenue:be,rideCount:1})});let Se=Array.from(E.entries()).map(([h,{revenue:b,rideCount:be}])=>{let C=o.find(P=>P.id===h);return{driverId:h,driverName:C?.full_name||"Unknown Driver",revenue:b,rideCount:be}}).sort((h,b)=>b.revenue-h.revenue).slice(0,5);return{dateRange:e,totalRevenue:m,revenueByDay:w,revenueByStatus:{pending:y,paid:R,failed:A,refunded:V},topDriversByRevenue:Se}})}generateUserActivityReport(t){return M(this,null,function*(){let{dateRange:e,userRole:i}=t,o=yield this.userService.getAllUsers(),l=yield this.rideService.getAllRides(),m=i?o.filter(C=>C.role===i):o,w=m.filter(C=>{let P=new Date(C.created_at);return P>=e.startDate&&P<=e.endDate}),y=w.filter(C=>C.role==="rider").length,R=w.filter(C=>C.role==="driver").length,A=new Set(l.filter(C=>{let P=new Date(C.created_at);return P>=e.startDate&&P<=e.endDate}).map(C=>C.rider_id)),V=new Set(l.filter(C=>{let P=new Date(C.created_at);return C.driver_id&&P>=e.startDate&&P<=e.endDate}).map(C=>C.driver_id)),E=m.filter(C=>C.role==="rider"&&A.has(C.id)).length,Se=m.filter(C=>C.role==="driver"&&V.has(C.id)).length,h=this.groupUsersByDay(w,l,e),b=new Map;l.filter(C=>{let P=new Date(C.created_at);return P>=e.startDate&&P<=e.endDate}).forEach(C=>{let P=C.rider_id,et=C.amount||C.fare||0;if(b.has(P)){let tt=b.get(P);b.set(P,{rideCount:tt.rideCount+1,totalSpent:tt.totalSpent+et})}else b.set(P,{rideCount:1,totalSpent:et})});let be=Array.from(b.entries()).map(([C,{rideCount:P,totalSpent:et}])=>{let tt=o.find(yn=>yn.id===C);return{riderId:C,riderName:tt?.full_name||"Unknown Rider",rideCount:P,totalSpent:et}}).sort((C,P)=>P.rideCount-C.rideCount).slice(0,5);return{dateRange:e,newUsers:{total:w.length,riders:y,drivers:R},activeUsers:{total:E+Se,riders:E,drivers:Se},usersByDay:h,topRiders:be}})}groupRidesByDay(t){let e=new Map;return t.forEach(i=>{let l=new Date(i.created_at).toISOString().split("T")[0];e.has(l)?e.set(l,e.get(l)+1):e.set(l,1)}),Array.from(e.entries()).map(([i,o])=>({date:i,count:o})).sort((i,o)=>i.date.localeCompare(o.date))}groupRevenueByDay(t){let e=new Map;return t.forEach(i=>{let l=new Date(i.created_at).toISOString().split("T")[0],m=i.amount||i.fare||0;e.has(l)?e.set(l,e.get(l)+m):e.set(l,m)}),Array.from(e.entries()).map(([i,o])=>({date:i,amount:o})).sort((i,o)=>i.date.localeCompare(o.date))}groupUsersByDay(t,e,i){let o=new Map,l=new Date(i.startDate);for(;l<=i.endDate;){let m=l.toISOString().split("T")[0];o.set(m,{newUsers:0,activeUsers:0}),l.setDate(l.getDate()+1)}return t.forEach(m=>{let y=new Date(m.created_at).toISOString().split("T")[0];if(o.has(y)){let R=o.get(y);o.set(y,He(je({},R),{newUsers:R.newUsers+1}))}}),e.forEach(m=>{let y=new Date(m.created_at).toISOString().split("T")[0];if(o.has(y)){let R=o.get(y);o.set(y,He(je({},R),{activeUsers:R.activeUsers+1}))}}),Array.from(o.entries()).map(([m,{newUsers:w,activeUsers:y}])=>({date:m,newUsers:w,activeUsers:y})).sort((m,w)=>m.date.localeCompare(w.date))}exportReportToCsv(t,e){let i="";switch(t){case"rides":i=this.exportRideReportToCsv(e);break;case"revenue":i=this.exportRevenueReportToCsv(e);break;case"users":i=this.exportUserReportToCsv(e);break}return i}exportRideReportToCsv(t){let e=`Report Type,Ride Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}

`,e+=`Rides by Status
`,e+=`Status,Count
`,e+=`Requested,${t.ridesByStatus.requested}
`,e+=`Assigned,${t.ridesByStatus.assigned}
`,e+=`In Progress,${t.ridesByStatus.inProgress}
`,e+=`Completed,${t.ridesByStatus.completed}
`,e+=`Canceled,${t.ridesByStatus.canceled}

`,e+=`Average Metrics
`,e+=`Average Duration (minutes),${t.averageDuration.toFixed(2)}
`,e+=`Average Distance (miles),${t.averageDistance.toFixed(2)}

`,e+=`Rides by Day
`,e+=`Date,Count
`,t.ridesByDay.forEach(i=>{e+=`${i.date},${i.count}
`}),e}exportRevenueReportToCsv(t){let e=`Report Type,Revenue Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}
`,e+=`Total Revenue,$${t.totalRevenue.toFixed(2)}

`,e+=`Revenue by Payment Status
`,e+=`Status,Amount
`,e+=`Pending,$${t.revenueByStatus.pending.toFixed(2)}
`,e+=`Paid,$${t.revenueByStatus.paid.toFixed(2)}
`,e+=`Failed,$${t.revenueByStatus.failed.toFixed(2)}
`,e+=`Refunded,$${t.revenueByStatus.refunded.toFixed(2)}

`,e+=`Top Drivers by Revenue
`,e+=`Driver,Revenue,Ride Count
`,t.topDriversByRevenue.forEach(i=>{e+=`${i.driverName},$${i.revenue.toFixed(2)},${i.rideCount}
`}),e+=`
`,e+=`Revenue by Day
`,e+=`Date,Amount
`,t.revenueByDay.forEach(i=>{e+=`${i.date},$${i.amount.toFixed(2)}
`}),e}exportUserReportToCsv(t){let e=`Report Type,User Activity Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}

`,e+=`New Users
`,e+=`Type,Count
`,e+=`Total,${t.newUsers.total}
`,e+=`Riders,${t.newUsers.riders}
`,e+=`Drivers,${t.newUsers.drivers}

`,e+=`Active Users
`,e+=`Type,Count
`,e+=`Total,${t.activeUsers.total}
`,e+=`Riders,${t.activeUsers.riders}
`,e+=`Drivers,${t.activeUsers.drivers}

`,e+=`Top Riders
`,e+=`Rider,Ride Count,Total Spent
`,t.topRiders.forEach(i=>{e+=`${i.riderName},${i.rideCount},$${i.totalSpent.toFixed(2)}
`}),e+=`
`,e+=`User Activity by Day
`,e+=`Date,New Users,Active Users
`,t.usersByDay.forEach(i=>{e+=`${i.date},${i.newUsers},${i.activeUsers}
`}),e}static \u0275fac=function(e){return new(e||n)(rt(Z),rt(pe),rt(Ne))};static \u0275prov=ai({token:n,factory:n.\u0275fac,providedIn:"root"})};var Er=["userSort"],Pr=["userPaginator"],Tr=["rideSort"],Ir=["ridePaginator"],Cn=()=>[5,10,25,100];function Ar(n,t){if(n&1&&(r(0,"div",46)(1,"mat-grid-list",47)(2,"mat-grid-tile")(3,"mat-card",48)(4,"mat-card-header")(5,"mat-card-title"),s(6,"User Statistics"),a()(),r(7,"mat-card-content")(8,"div",49)(9,"div",50)(10,"div",51),s(11),a(),r(12,"div",52),s(13,"Total Users"),a()(),r(14,"div",50)(15,"div",51),s(16),a(),r(17,"div",52),s(18,"Riders"),a()(),r(19,"div",50)(20,"div",51),s(21),a(),r(22,"div",52),s(23,"Drivers"),a()(),r(24,"div",50)(25,"div",51),s(26),a(),r(27,"div",52),s(28,"Admins"),a()()()()()(),r(29,"mat-grid-tile")(30,"mat-card",48)(31,"mat-card-header")(32,"mat-card-title"),s(33,"Ride Statistics"),a()(),r(34,"mat-card-content")(35,"div",49)(36,"div",50)(37,"div",51),s(38),a(),r(39,"div",52),s(40,"Total Rides"),a()(),r(41,"div",50)(42,"div",51),s(43),a(),r(44,"div",52),s(45,"Requested"),a()(),r(46,"div",50)(47,"div",51),s(48),a(),r(49,"div",52),s(50,"In Progress"),a()(),r(51,"div",50)(52,"div",51),s(53),a(),r(54,"div",52),s(55,"Completed"),a()()()()()()()()),n&2){let e=u();d(11),D(e.statistics.totalUsers.all),d(5),D(e.statistics.totalUsers.riders),d(5),D(e.statistics.totalUsers.drivers),d(5),D(e.statistics.totalUsers.admins),d(12),D(e.statistics.rides.total),d(5),D(e.statistics.rides.requested),d(5),D(e.statistics.rides.inProgress),d(5),D(e.statistics.rides.completed)}}function Fr(n,t){n&1&&(r(0,"div",53),v(1,"mat-spinner",54),r(2,"p"),s(3,"Loading statistics..."),a()())}function Or(n,t){n&1&&(r(0,"th",72),s(1,"Email"),a())}function Vr(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit;d(),D(e.email)}}function Br(n,t){n&1&&(r(0,"th",72),s(1,"Name"),a())}function Ur(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit;d(),D(e.full_name||"N/A")}}function zr(n,t){n&1&&(r(0,"th",72),s(1,"Role"),a())}function Nr(n,t){if(n&1&&(r(0,"td",73)(1,"mat-chip",74),s(2),a()()),n&2){let e=t.$implicit,i=u(2);d(),c("color",e.role==="admin"?"warn":"primary"),d(),S(" ",i.getRoleDisplayName(e.role)," ")}}function $r(n,t){n&1&&(r(0,"th",72),s(1,"Registered"),a())}function Lr(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit,i=u(2);d(),D(i.formatDate(e.created_at))}}function qr(n,t){n&1&&(r(0,"th",75),s(1,"Status"),a())}function jr(n,t){if(n&1&&(r(0,"td",73)(1,"mat-chip",74),s(2),a()()),n&2){let e=t.$implicit;d(),c("color",e.is_approved?"accent":"warn"),d(),S(" ",e.is_approved?"Active":e.role==="driver"?"Pending Approval":"Inactive"," ")}}function Hr(n,t){n&1&&(r(0,"th",75),s(1,"Actions"),a())}function Gr(n,t){if(n&1){let e=k();r(0,"button",78),f("click",function(){g(e);let o=u().$implicit,l=u(2);return _(l.approveDriver(o.id))}),r(1,"mat-icon"),s(2,"check_circle"),a()()}}function Qr(n,t){if(n&1){let e=k();r(0,"td",73),p(1,Gr,3,0,"button",76),r(2,"button",77),f("click",function(){let o=g(e).$implicit,l=u(2);return _(l.openUserDetails(o))}),r(3,"mat-icon"),s(4,"visibility"),a()()()}if(n&2){let e=t.$implicit;d(),c("ngIf",e.role==="driver"&&!e.is_approved)}}function Wr(n,t){n&1&&v(0,"tr",79)}function Xr(n,t){n&1&&v(0,"tr",80)}function Jr(n,t){if(n&1){let e=k();r(0,"button",78),f("click",function(o){g(e);let l=u().$implicit;return u(2).approveDriver(l.id),_(o.stopPropagation())}),r(1,"mat-icon"),s(2,"check_circle"),a()()}}function Kr(n,t){if(n&1){let e=k();r(0,"mat-expansion-panel")(1,"mat-expansion-panel-header")(2,"mat-panel-title"),s(3),a(),r(4,"mat-panel-description")(5,"div",81)(6,"mat-chip",82),s(7),a(),p(8,Jr,3,0,"button",76),r(9,"button",77),f("click",function(o){let l=g(e).$implicit;return u(2).openUserDetails(l),_(o.stopPropagation())}),r(10,"mat-icon"),s(11,"visibility"),a()()()()(),r(12,"div",83)(13,"p")(14,"strong"),s(15,"Email:"),a(),s(16),a(),r(17,"p")(18,"strong"),s(19,"Name:"),a(),s(20),a(),r(21,"p")(22,"strong"),s(23,"Status:"),a(),r(24,"mat-chip",74),s(25),a()(),r(26,"p")(27,"strong"),s(28,"Registered:"),a(),s(29),a()()()}if(n&2){let e=t.$implicit,i=u(2);d(3),S(" ",e.full_name||e.email," "),d(3),c("color",e.role==="admin"?"warn":"primary"),d(),S(" ",i.getRoleDisplayName(e.role)," "),d(),c("ngIf",e.role==="driver"&&!e.is_approved),d(8),S(" ",e.email,""),d(4),S(" ",e.full_name||"N/A",""),d(4),c("color",e.is_approved?"accent":"warn"),d(),S(" ",e.is_approved?"Active":e.role==="driver"?"Pending Approval":"Inactive"," "),d(4),S(" ",i.formatDate(e.created_at),"")}}function Zr(n,t){if(n&1&&(r(0,"div")(1,"div",55)(2,"table",56,3),T(4,57),p(5,Or,2,0,"th",58)(6,Vr,2,1,"td",59),I(),T(7,60),p(8,Br,2,0,"th",58)(9,Ur,2,1,"td",59),I(),T(10,61),p(11,zr,2,0,"th",58)(12,Nr,3,2,"td",59),I(),T(13,62),p(14,$r,2,0,"th",58)(15,Lr,2,1,"td",59),I(),T(16,63),p(17,qr,2,0,"th",64)(18,jr,3,2,"td",59),I(),T(19,65),p(20,Hr,2,0,"th",64)(21,Qr,5,1,"td",59),I(),p(22,Wr,1,0,"tr",66)(23,Xr,1,0,"tr",67),a(),v(24,"mat-paginator",68,4),a(),r(26,"div",69)(27,"mat-accordion",70),p(28,Kr,30,9,"mat-expansion-panel",71),a()()()),n&2){let e=u();d(2),c("dataSource",e.userDataSource),d(20),c("matHeaderRowDef",e.userDisplayedColumns),d(),c("matRowDefColumns",e.userDisplayedColumns),d(),c("pageSizeOptions",Lt(7,Cn))("pageSize",10),d(4),c("ngForOf",e.filteredUsers())("ngForTrackBy",e.trackByUserId)}}function Yr(n,t){n&1&&(r(0,"div",53),v(1,"mat-spinner",54),r(2,"p"),s(3,"Loading users..."),a()())}function ea(n,t){n&1&&(r(0,"th",72),s(1,"Rider"),a())}function ta(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit,i=u(2);d(),D(i.getUserName(e.rider_id))}}function ia(n,t){n&1&&(r(0,"th",72),s(1,"Driver"),a())}function na(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit,i=u(2);d(),D(e.driver_id?i.getUserName(e.driver_id):"Not Assigned")}}function ra(n,t){n&1&&(r(0,"th",72),s(1,"Pickup"),a())}function aa(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit;d(),D(e.pickup_location)}}function oa(n,t){n&1&&(r(0,"th",72),s(1,"Dropoff"),a())}function sa(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit;d(),D(e.dropoff_location)}}function la(n,t){n&1&&(r(0,"th",72),s(1,"Price"),a())}function da(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit;d(),S("$",e.fare||"N/A","")}}function ca(n,t){n&1&&(r(0,"th",72),s(1,"Status"),a())}function ma(n,t){if(n&1&&(r(0,"td",73)(1,"mat-chip",74),s(2),a()()),n&2){let e=t.$implicit,i=u(2);d(),c("color",i.getStatusColor(e.status)),d(),S(" ",i.getStatusDisplayName(e.status)," ")}}function pa(n,t){n&1&&(r(0,"th",72),s(1,"Created"),a())}function ua(n,t){if(n&1&&(r(0,"td",73),s(1),a()),n&2){let e=t.$implicit,i=u(2);d(),D(i.formatDate(e.created_at))}}function ga(n,t){n&1&&(r(0,"th",75),s(1,"Actions"),a())}function _a(n,t){if(n&1){let e=k();r(0,"button",96),f("click",function(){g(e);let o=u().$implicit,l=u(2);return _(l.openDriverSelectionDialog(o.id))}),r(1,"mat-icon"),s(2,"person_add"),a()()}}function fa(n,t){if(n&1){let e=k();r(0,"button",97),f("click",function(){g(e);let o=u().$implicit,l=u(2);return _(l.openDriverSelectionDialog(o.id))}),r(1,"mat-icon"),s(2,"swap_horiz"),a()()}}function ha(n,t){if(n&1){let e=k();r(0,"button",98),f("click",function(){g(e);let o=u().$implicit,l=u(2);return _(l.updateRideStatus(o.id,"canceled"))}),r(1,"mat-icon"),s(2,"cancel"),a()()}}function va(n,t){if(n&1){let e=k();r(0,"button",99),f("click",function(){g(e);let o=u().$implicit,l=u(2);return _(l.updateRideStatus(o.id,"in-progress"))}),r(1,"mat-icon"),s(2,"play_arrow"),a()()}}function wa(n,t){if(n&1){let e=k();r(0,"button",100),f("click",function(){g(e);let o=u().$implicit,l=u(2);return _(l.updateRideStatus(o.id,"completed"))}),r(1,"mat-icon"),s(2,"check_circle"),a()()}}function Sa(n,t){if(n&1){let e=k();r(0,"td",73),p(1,_a,3,0,"button",91)(2,fa,3,0,"button",92)(3,ha,3,0,"button",93)(4,va,3,0,"button",94)(5,wa,3,0,"button",95),r(6,"button",77),f("click",function(){let o=g(e).$implicit,l=u(2);return _(l.viewRideDetails(o.id))}),r(7,"mat-icon"),s(8,"visibility"),a()()()}if(n&2){let e=t.$implicit;d(),c("ngIf",e.status==="requested"),d(),c("ngIf",e.status==="assigned"),d(),c("ngIf",e.status==="requested"||e.status==="assigned"),d(),c("ngIf",e.status==="assigned"),d(),c("ngIf",e.status==="in-progress")}}function ba(n,t){n&1&&v(0,"tr",79)}function Ca(n,t){n&1&&v(0,"tr",80)}function ya(n,t){if(n&1){let e=k();r(0,"button",96),f("click",function(o){g(e);let l=u().$implicit;return u(2).openDriverSelectionDialog(l.id),_(o.stopPropagation())}),r(1,"mat-icon"),s(2,"person_add"),a()()}}function xa(n,t){if(n&1){let e=k();r(0,"button",97),f("click",function(o){g(e);let l=u().$implicit;return u(2).openDriverSelectionDialog(l.id),_(o.stopPropagation())}),r(1,"mat-icon"),s(2,"swap_horiz"),a()()}}function Ma(n,t){if(n&1){let e=k();r(0,"button",98),f("click",function(){g(e);let o=u().$implicit,l=u(2);return _(l.updateRideStatus(o.id,"canceled"))}),r(1,"mat-icon"),s(2,"cancel"),a()()}}function Da(n,t){if(n&1){let e=k();r(0,"button",100),f("click",function(o){g(e);let l=u().$implicit;return u(2).updateRideStatus(l.id,"completed"),_(o.stopPropagation())}),r(1,"mat-icon"),s(2,"check_circle"),a()()}}function ka(n,t){if(n&1){let e=k();r(0,"button",99),f("click",function(o){g(e);let l=u().$implicit;return u(2).updateRideStatus(l.id,"in-progress"),_(o.stopPropagation())}),r(1,"mat-icon"),s(2,"play_arrow"),a()()}}function Ra(n,t){if(n&1){let e=k();r(0,"mat-expansion-panel")(1,"mat-expansion-panel-header")(2,"mat-panel-title"),s(3),a(),r(4,"mat-panel-description")(5,"div",101)(6,"mat-chip",102),s(7),a(),p(8,ya,3,0,"button",91)(9,xa,3,0,"button",92)(10,Ma,3,0,"button",93)(11,Da,3,0,"button",95),a()()(),r(12,"div",103)(13,"p")(14,"strong"),s(15,"Driver:"),a(),s(16),a(),r(17,"p")(18,"strong"),s(19,"Pickup:"),a(),s(20),a(),r(21,"p")(22,"strong"),s(23,"To:"),a(),s(24),a(),r(25,"p")(26,"strong"),s(27,"Fare:"),a(),s(28),a(),r(29,"p")(30,"strong"),s(31,"Created:"),a(),s(32),a()(),r(33,"mat-action-row"),p(34,ka,3,0,"button",94),r(35,"button",77),f("click",function(){let o=g(e).$implicit,l=u(2);return _(l.viewRideDetails(o.id))}),r(36,"mat-icon"),s(37,"visibility"),a()()()()}if(n&2){let e=t.$implicit,i=u(2);d(3),S(" ",e.rider_id?i.getUserName(e.rider_id):"N/A"," "),d(3),c("color",i.getStatusColor(e.status)),d(),S(" ",i.getStatusDisplayName(e.status)," "),d(),c("ngIf",e.status==="requested"),d(),c("ngIf",e.status==="assigned"),d(),c("ngIf",e.status==="requested"||e.status==="assigned"),d(),c("ngIf",e.status==="in-progress"),d(5),S(" ",e.driver_id?i.getUserName(e.driver_id):"Not Assigned",""),d(4),S(" ",e.pickup_location,""),d(4),S(" ",e.dropoff_location,""),d(4),S(" $",e.fare||"N/A",""),d(4),S(" ",i.formatDate(e.created_at),""),d(2),c("ngIf",e.status==="assigned")}}function Ea(n,t){if(n&1&&(r(0,"div")(1,"div",55)(2,"table",84,5),T(4,85),p(5,ea,2,0,"th",58)(6,ta,2,1,"td",59),I(),T(7,86),p(8,ia,2,0,"th",58)(9,na,2,1,"td",59),I(),T(10,87),p(11,ra,2,0,"th",58)(12,aa,2,1,"td",59),I(),T(13,88),p(14,oa,2,0,"th",58)(15,sa,2,1,"td",59),I(),T(16,89),p(17,la,2,0,"th",58)(18,da,2,1,"td",59),I(),T(19,63),p(20,ca,2,0,"th",58)(21,ma,3,2,"td",59),I(),T(22,62),p(23,pa,2,0,"th",58)(24,ua,2,1,"td",59),I(),T(25,65),p(26,ga,2,0,"th",64)(27,Sa,9,5,"td",59),I(),p(28,ba,1,0,"tr",66)(29,Ca,1,0,"tr",67),a(),v(30,"mat-paginator",90,6),a(),r(32,"div",69)(33,"mat-accordion",70),p(34,Ra,38,13,"mat-expansion-panel",71),a()()()),n&2){let e=u();d(2),c("dataSource",e.rideDataSource)("trackBy",e.trackByRideId),d(26),c("matHeaderRowDef",e.rideDisplayedColumns),d(),c("matRowDefColumns",e.rideDisplayedColumns),d(),c("length",e.rides.length)("pageSizeOptions",Lt(9,Cn))("pageSize",5),d(4),c("ngForOf",e.filteredRides())("ngForTrackBy",e.trackByRideId)}}function Pa(n,t){n&1&&(r(0,"div",53),v(1,"mat-spinner",54),r(2,"p"),s(3,"Loading rides..."),a()())}function Ta(n,t){if(n&1){let e=k();r(0,"div",104)(1,"app-ride-detail",105),f("rideUpdated",function(o){g(e);let l=u();return _(l.onRideUpdated(o))}),a()()}if(n&2){let e=u();d(),c("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}function Ia(n,t){if(n&1){let e=k();r(0,"div",106),f("click",function(){g(e);let o=u();return _(o.closeRideDetails())}),r(1,"mat-icon"),s(2,"close"),a()()}}var bn=class n{constructor(t,e,i,o,l){this.userService=t;this.rideService=e;this.statisticsService=i;this.snackBar=o;this.dialog=l}usersSignal=fe([]);users=[];userDataSource=new Gt([]);userDisplayedColumns=["email","full_name","role","created_at","status","actions"];userRoleFilter="";userSearchTerm="";loadingUsers=!1;userRoleFilterSignal=fe("all");userSearchTermSignal=fe("");filteredUsers=qt(()=>{let t=this.usersSignal(),e=this.userRoleFilterSignal(),i=this.userSearchTermSignal().toLowerCase().trim(),o=t;return e!=="all"&&(o=o.filter(l=>l.role===e)),i&&(o=o.filter(l=>l.full_name&&l.full_name.toLowerCase().includes(i)||l.email&&l.email.toLowerCase().includes(i))),o});ridesSignal=fe([]);rides=[];rideDataSource=new Gt([]);rideDisplayedColumns=["rider_id","driver_id","pickup_location","dropoff_location","status","created_at","actions"];rideStatusFilter="";rideSearchTerm="";loadingRides=!1;selectedRideId=null;lastRideRefreshTime=new Date;rideStatusFilterSignal=fe("all");rideSearchTermSignal=fe("");filteredRides=qt(()=>{let t=this.ridesSignal(),e=this.rideStatusFilterSignal(),i=this.rideSearchTermSignal().toLowerCase().trim(),o=t;return e!=="all"&&(o=o.filter(l=>l.status===e)),i&&(o=o.filter(l=>l.pickup_location&&l.pickup_location.toLowerCase().includes(i)||l.dropoff_location&&l.dropoff_location.toLowerCase().includes(i)||this.getUserName(l.rider_id)&&this.getUserName(l.rider_id).toLowerCase().includes(i)||l.driver_id&&this.getUserName(l.driver_id)&&this.getUserName(l.driver_id).toLowerCase().includes(i))),o});statistics=null;loadingStatistics=!1;userNameCache={};userNameLoadingCache={};ridesSubscription=null;usersSubscription=null;userSort;userPaginator;rideSort;ridePaginator;dataLoadingComplete=!1;ngOnInit(){return M(this,null,function*(){console.log("Admin Component ngOnInit");try{this.setupUserSubscription(),yield Promise.all([this.initialLoadRides(),this.loadStatistics()]),this.dataLoadingComplete=!0,this.userPaginator&&this.ridePaginator&&this.setupPaginators(),this.setupRideSubscription()}catch(t){console.error("Error loading data:",t),this.snackBar.open("Failed to load data","Close",{duration:3e3})}})}initialLoadRides(){return M(this,null,function*(){this.loadingRides=!0;try{this.rides=yield this.rideService.getAllRides(),this.ridesSignal.set(this.rides),this.rideDataSource.data=this.rides,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.lastRideRefreshTime=new Date}catch(t){throw console.error("Error loading rides:",t),t}finally{this.loadingRides=!1}this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator})}setupRideSubscription(){this.ridesSubscription&&this.ridesSubscription.unsubscribe(),this.ridesSubscription=this.rideService.rides$.subscribe(t=>{this.rides=t,this.ridesSignal.set(t),this.rideDataSource.data=t,this.dataLoadingComplete&&(this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator),(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.lastRideRefreshTime=new Date})}ngAfterViewInit(){this.dataLoadingComplete&&this.setupPaginators()}setupPaginators(){this.userDataSource.sort=this.userSort,this.userDataSource.paginator=this.userPaginator,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,this.userDataSource.filterPredicate=(t,e)=>{let i=JSON.parse(e),o=!0,l=!0;if(i.role&&(o=t.role===i.role),i.searchTerm){let m=i.searchTerm.toLowerCase(),w=t.email.toLowerCase().includes(m),y=t.full_name?t.full_name.toLowerCase().includes(m):!1;l=w||y}return o&&l},this.userDataSource.sortingDataAccessor=(t,e)=>{switch(e){case"created_at":return new Date(t.created_at).getTime();case"full_name":return t.full_name?.toLowerCase()||"";default:return t[e]||""}},this.rideDataSource.filterPredicate=(t,e)=>{let i=JSON.parse(e),o=!0,l=!0;if(i.status&&(o=t.status===i.status),i.searchTerm){let m=i.searchTerm.toLowerCase(),w=t.pickup_location.toLowerCase().includes(m),y=t.dropoff_location.toLowerCase().includes(m),R=(this.getUserName(t.rider_id)||"").toLowerCase().includes(m);l=w||y||R}return o&&l},this.rideDataSource.sortingDataAccessor=(t,e)=>{switch(console.log("SORTING ASSESOR ",e),e){case"rider_id":return this.getUserName(t.rider_id).toLowerCase();case"driver_id":return t.driver_id?this.getUserName(t.driver_id).toLowerCase():"zzz";case"created_at":case"pickup_time":return t[e]?new Date(t[e]).getTime():0;case"fare":return t.fare||0;default:return t[e]||""}}}setupUserSubscription(){this.loadingUsers=!0,this.usersSubscription&&this.usersSubscription.unsubscribe(),this.usersSubscription=this.userService.users$.subscribe(t=>{this.users=t,this.usersSignal.set(t),this.userDataSource.data=t,this.dataLoadingComplete&&(this.userDataSource.sort=this.userSort,this.userDataSource.paginator=this.userPaginator),(this.userRoleFilter||this.userSearchTerm)&&this.applyUserFilters(),this.loadingUsers=!1},t=>{console.error("Error loading users:",t),this.snackBar.open("Failed to load users","Close",{duration:3e3}),this.loadingUsers=!1})}applyUserFilters(){let t=JSON.stringify({role:this.userRoleFilter,searchTerm:this.userSearchTerm});this.userDataSource.filter=t,this.userDataSource.paginator&&this.dataLoadingComplete&&this.userDataSource.paginator.firstPage()}getRoleDisplayName(t){return t.charAt(0).toUpperCase()+t.slice(1)}approveDriver(t){return M(this,null,function*(){try{if(yield this.userService.approveDriver(t))this.snackBar.open("Driver approved successfully","Close",{duration:3e3});else throw new Error("Failed to approve driver")}catch(e){console.error("Error approving driver:",e),this.snackBar.open("Failed to approve driver","Close",{duration:3e3})}})}openUserDetails(t){this.dialog.open(Vt,{width:"500px",data:t})}getStatusDisplay(t){return t.role==="driver"?t.is_approved?"Approved":"Pending Approval":t.role==="admin"?t.is_approved?"Active":"Inactive":"Active"}loadRides(){return M(this,null,function*(){this.loadingRides=!0;try{yield this.rideService.getAllRides(),this.lastRideRefreshTime=new Date}catch(t){console.error("Error loading rides:",t),this.snackBar.open("Failed to load rides","Close",{duration:3e3})}finally{this.loadingRides=!1}})}applyRideFilters(){let t=JSON.stringify({status:this.rideStatusFilter,searchTerm:this.rideSearchTerm});this.rideDataSource.filter=t,this.rideDataSource.paginator&&this.dataLoadingComplete&&this.rideDataSource.paginator.firstPage()}updateRideStatus(t,e){return M(this,null,function*(){try{if(yield this.rideService.updateRideStatus(t,e))this.snackBar.open(`Ride status updated to ${this.getStatusDisplayName(e)}`,"Close",{duration:3e3});else throw new Error("Failed to update ride status")}catch(i){console.error("Error updating ride status:",i),this.snackBar.open("Failed to update ride status","Close",{duration:3e3})}})}openDriverSelectionDialog(t){this.dialog.open(Et,{width:"500px",data:{drivers:this.users.filter(i=>i.role==="driver"&&i.is_approved)}}).afterClosed().subscribe(i=>M(this,null,function*(){if(i)try{if(yield this.rideService.assignRideToDriver(t,i))this.snackBar.open("Driver assigned successfully","Close",{duration:3e3});else throw new Error("Failed to assign driver")}catch(o){console.error("Error assigning driver:",o),this.snackBar.open("Failed to assign driver","Close",{duration:3e3})}}))}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null}onRideUpdated(t){}openCreateRideDialog(){this.dialog.open(zt,{width:"600px"}).afterClosed().subscribe(e=>{e&&this.snackBar.open("Ride created successfully","Close",{duration:3e3})})}loadStatistics(){return M(this,null,function*(){this.loadingStatistics=!0;try{this.statistics=yield this.statisticsService.generateSystemStatistics()}catch(t){console.error("Error loading statistics:",t),this.snackBar.open("Failed to load statistics","Close",{duration:3e3})}finally{this.loadingStatistics=!1}})}getStatusDisplayName(t){return t.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}getStatusColor(t){return{requested:"warn",assigned:"primary","in-progress":"accent",completed:"primary",canceled:"warn"}[t]||"primary"}formatDate(t){return new Date(t).toLocaleString()}getUserName(t){return t&&(this.users.find(i=>i.id===t)?.full_name||t)||"N/A"}trackByUserId(t,e){return e.id}trackByRideId(t,e){return e.id}ngOnDestroy(){this.ridesSubscription&&this.ridesSubscription.unsubscribe(),this.usersSubscription&&this.usersSubscription.unsubscribe()}static \u0275fac=function(e){return new(e||n)(x(Z),x(pe),x(Nt),x($),x(Ci))};static \u0275cmp=O({type:n,selectors:[["app-admin"]],viewQuery:function(e,i){if(e&1&&(Y(Er,5),Y(Pr,5),Y(Tr,5),Y(Ir,5)),e&2){let o;L(o=q())&&(i.userSort=o.first),L(o=q())&&(i.userPaginator=o.first),L(o=q())&&(i.rideSort=o.first),L(o=q())&&(i.ridePaginator=o.first)}},decls:124,vars:25,consts:[["loadingStats",""],["loadingUsersTemplate",""],["loadingRidesTemplate",""],["userSort","matSort"],["userPaginator",""],["rideSort","matSort"],["ridePaginator",""],[1,"dashboard-container"],[1,"dashboard-title"],["animationDuration","300ms"],["label","Dashboard Overview"],[1,"tab-content"],["class","stats-container",4,"ngIf","ngIfElse"],["label","User Management"],[1,"filters-container","desktop-filters"],["appearance","outline"],[3,"ngModelChange","selectionChange","ngModel"],["value",""],["value","rider"],["value","driver"],["value","admin"],["matInput","","placeholder","Search by name or email",3,"ngModelChange","keyup","ngModel"],["matSuffix",""],[1,"mobile-filter-container"],["aria-label","Filter users by role",1,"filter-buttons"],["mat-stroked-button","",1,"filter-button",3,"click","color"],[1,"mobile-search"],[4,"ngIf","ngIfElse"],["label","Ride Management"],[1,"refresh-info"],[1,"last-refresh"],["mat-icon-button","","color","primary","matTooltip","Refresh rides manually",3,"click"],[1,"auto-refresh-note"],[1,"action-buttons"],["mat-raised-button","","color","primary",3,"click"],["value","requested"],["value","assigned"],["value","in-progress"],["value","completed"],["value","canceled"],["matInput","","placeholder","Search by location or name",3,"ngModelChange","keyup","ngModel"],["aria-label","Filter rides by status",1,"filter-buttons"],["label","Stripe Payment Processing"],["label","Ride Pricing"],["class","ride-detail-overlay",4,"ngIf"],["class","close-overlay",3,"click",4,"ngIf"],[1,"stats-container"],["cols","2","rowHeight","250px","gutterSize","16px"],[1,"stats-card"],[1,"stats-grid"],[1,"stat-item"],[1,"stat-value"],[1,"stat-label"],[1,"loading-container"],["diameter","50"],[1,"desktop-view"],["mat-table","","matSort","",1,"mat-elevation-z2","full-width",3,"dataSource"],["matColumnDef","email"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","full_name"],["matColumnDef","role"],["matColumnDef","created_at"],["matColumnDef","status"],["mat-header-cell","",4,"matHeaderCellDef"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["showFirstLastButtons","","aria-label","Select page of users",3,"pageSizeOptions","pageSize"],[1,"mobile-view"],["multi",""],[4,"ngFor","ngForOf","ngForTrackBy"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],["selected","",3,"color"],["mat-header-cell",""],["mat-icon-button","","color","primary","matTooltip","Approve Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","View Details",3,"click"],["mat-icon-button","","color","primary","matTooltip","Approve Driver",3,"click"],["mat-header-row",""],["mat-row",""],[1,"user-actions-header"],["selected","",1,"role-chip",3,"color"],[1,"user-details"],["mat-table","","matSort","",1,"mat-elevation-z2","full-width",3,"dataSource","trackBy"],["matColumnDef","rider_id"],["matColumnDef","driver_id"],["matColumnDef","pickup_location"],["matColumnDef","dropoff_location"],["matColumnDef","price"],["showFirstLastButtons","","aria-label","Select page of rides",3,"length","pageSizeOptions","pageSize"],["mat-icon-button","","color","primary","matTooltip","Assign Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Reassign Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","Cancel Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Start Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Complete Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Assign Driver",3,"click"],["mat-icon-button","","color","primary","matTooltip","Reassign Driver",3,"click"],["mat-icon-button","","color","accent","matTooltip","Cancel Ride",3,"click"],["mat-icon-button","","color","warn","matTooltip","Start Ride",3,"click"],["mat-icon-button","","color","primary","matTooltip","Complete Ride",3,"click"],[1,"ride-actions-header"],["selected","",1,"status-chip",3,"color"],[1,"ride-details"],[1,"ride-detail-overlay"],[3,"rideUpdated","rideId","onClose"],[1,"close-overlay",3,"click"]],template:function(e,i){if(e&1){let o=k();r(0,"div",7)(1,"h1",8),s(2,"Admin Dashboard"),a(),r(3,"mat-tab-group",9)(4,"mat-tab",10)(5,"div",11),p(6,Ar,56,8,"div",12)(7,Fr,4,0,"ng-template",null,0,st),a()(),r(9,"mat-tab",13)(10,"div",11)(11,"mat-card")(12,"mat-card-header")(13,"mat-card-title"),s(14,"User Management"),a()(),r(15,"mat-card-content")(16,"div",14)(17,"mat-form-field",15)(18,"mat-select",16),ne("ngModelChange",function(m){return g(o),ie(i.userRoleFilter,m)||(i.userRoleFilter=m),_(m)}),f("selectionChange",function(){return g(o),_(i.applyUserFilters())}),r(19,"mat-option",17),s(20,"All Roles"),a(),r(21,"mat-option",18),s(22,"Rider"),a(),r(23,"mat-option",19),s(24,"Driver"),a(),r(25,"mat-option",20),s(26,"Admin"),a()()(),r(27,"mat-form-field",15)(28,"mat-label"),s(29,"Search"),a(),r(30,"input",21),ne("ngModelChange",function(m){return g(o),ie(i.userSearchTerm,m)||(i.userSearchTerm=m),_(m)}),f("keyup",function(){return g(o),i.applyUserFilters(),_(i.userSearchTermSignal.set(i.userSearchTerm))}),a(),r(31,"mat-icon",22),s(32,"search"),a()()(),r(33,"div",23)(34,"div",24)(35,"button",25),f("click",function(){return g(o),_(i.userRoleFilterSignal.set("all"))}),s(36,"All"),a(),r(37,"button",25),f("click",function(){return g(o),_(i.userRoleFilterSignal.set("rider"))}),s(38,"Riders"),a(),r(39,"button",25),f("click",function(){return g(o),_(i.userRoleFilterSignal.set("driver"))}),s(40,"Drivers"),a(),r(41,"button",25),f("click",function(){return g(o),_(i.userRoleFilterSignal.set("admin"))}),s(42,"Admins"),a()(),r(43,"div",26)(44,"mat-form-field",15)(45,"mat-label"),s(46,"Search"),a(),r(47,"input",21),ne("ngModelChange",function(m){return g(o),ie(i.userSearchTerm,m)||(i.userSearchTerm=m),_(m)}),f("keyup",function(){return g(o),i.applyUserFilters(),_(i.userSearchTermSignal.set(i.userSearchTerm))}),a(),r(48,"mat-icon",22),s(49,"search"),a()()()(),p(50,Zr,29,8,"div",27)(51,Yr,4,0,"ng-template",null,1,st),a()()()(),r(53,"mat-tab",28)(54,"div",11)(55,"mat-card")(56,"mat-card-header")(57,"mat-card-title"),s(58,"Ride Management"),a(),r(59,"div",29)(60,"span",30),s(61),a(),r(62,"button",31),f("click",function(){return g(o),_(i.loadRides())}),r(63,"mat-icon"),s(64,"refresh"),a()(),r(65,"span",32),s(66,"(Real-time updates)"),a()(),r(67,"div",33)(68,"button",34),f("click",function(){return g(o),_(i.openCreateRideDialog())}),r(69,"mat-icon"),s(70,"add"),a(),s(71," Create Ride "),a()()(),r(72,"mat-card-content")(73,"div",14)(74,"mat-form-field",15)(75,"mat-select",16),ne("ngModelChange",function(m){return g(o),ie(i.rideStatusFilter,m)||(i.rideStatusFilter=m),_(m)}),f("selectionChange",function(){return g(o),_(i.applyRideFilters())}),r(76,"mat-option",17),s(77,"All Statuses"),a(),r(78,"mat-option",35),s(79,"Requested"),a(),r(80,"mat-option",36),s(81,"Assigned"),a(),r(82,"mat-option",37),s(83,"In Progress"),a(),r(84,"mat-option",38),s(85,"Completed"),a(),r(86,"mat-option",39),s(87,"Canceled"),a()()(),r(88,"mat-form-field",15)(89,"input",40),ne("ngModelChange",function(m){return g(o),ie(i.rideSearchTerm,m)||(i.rideSearchTerm=m),_(m)}),f("keyup",function(){return g(o),i.applyRideFilters(),_(i.rideSearchTermSignal.set(i.rideSearchTerm))}),a(),r(90,"mat-icon",22),s(91,"search"),a()()(),r(92,"div",23)(93,"div",41)(94,"button",25),f("click",function(){return g(o),_(i.rideStatusFilterSignal.set("all"))}),s(95,"All"),a(),r(96,"button",25),f("click",function(){return g(o),_(i.rideStatusFilterSignal.set("requested"))}),s(97,"Requested"),a(),r(98,"button",25),f("click",function(){return g(o),_(i.rideStatusFilterSignal.set("assigned"))}),s(99,"Assigned"),a(),r(100,"button",25),f("click",function(){return g(o),_(i.rideStatusFilterSignal.set("in-progress"))}),s(101,"In Progress"),a(),r(102,"button",25),f("click",function(){return g(o),_(i.rideStatusFilterSignal.set("completed"))}),s(103,"Completed"),a(),r(104,"button",25),f("click",function(){return g(o),_(i.rideStatusFilterSignal.set("canceled"))}),s(105,"Canceled"),a()(),r(106,"div",26)(107,"mat-form-field",15)(108,"mat-label"),s(109,"Search"),a(),r(110,"input",40),ne("ngModelChange",function(m){return g(o),ie(i.rideSearchTerm,m)||(i.rideSearchTerm=m),_(m)}),f("keyup",function(){return g(o),i.applyRideFilters(),_(i.rideSearchTermSignal.set(i.rideSearchTerm))}),a(),r(111,"mat-icon",22),s(112,"search"),a()()()(),p(113,Ea,35,10,"div",27)(114,Pa,4,0,"ng-template",null,2,st),a()()()(),r(116,"mat-tab",42)(117,"div",11),v(118,"app-stripe-payment"),a()(),r(119,"mat-tab",43)(120,"div",11),v(121,"app-ride-pricing"),a()()()(),p(122,Ta,2,2,"div",44)(123,Ia,3,0,"div",45)}if(e&2){let o=ee(8),l=ee(52),m=ee(115);d(6),c("ngIf",i.statistics)("ngIfElse",o),d(12),te("ngModel",i.userRoleFilter),d(12),te("ngModel",i.userSearchTerm),d(5),c("color",i.userRoleFilterSignal()==="all"?"primary":""),d(2),c("color",i.userRoleFilterSignal()==="rider"?"primary":""),d(2),c("color",i.userRoleFilterSignal()==="driver"?"primary":""),d(2),c("color",i.userRoleFilterSignal()==="admin"?"primary":""),d(6),te("ngModel",i.userSearchTerm),d(3),c("ngIf",!i.loadingUsers)("ngIfElse",l),d(11),S("Last refreshed: ",i.formatDate(i.lastRideRefreshTime.toISOString()),""),d(14),te("ngModel",i.rideStatusFilter),d(14),te("ngModel",i.rideSearchTerm),d(5),c("color",i.rideStatusFilterSignal()==="all"?"primary":""),d(2),c("color",i.rideStatusFilterSignal()==="requested"?"primary":""),d(2),c("color",i.rideStatusFilterSignal()==="assigned"?"primary":""),d(2),c("color",i.rideStatusFilterSignal()==="in-progress"?"primary":""),d(2),c("color",i.rideStatusFilterSignal()==="completed"?"primary":""),d(2),c("color",i.rideStatusFilterSignal()==="canceled"?"primary":""),d(6),te("ngModel",i.rideSearchTerm),d(3),c("ngIf",!i.loadingRides)("ngIfElse",m),d(9),c("ngIf",i.selectedRideId),d(),c("ngIf",i.selectedRideId)}},dependencies:[U,Ce,j,G,oe,H,mt,se,K,Ve,Ue,ze,Be,Wi,Gi,Qi,Le,ft,vt,Ct,wt,ht,yt,St,bt,xt,Mt,ln,on,sn,cn,dn,me,ce,W,Q,ut,X,le,Oe,Ae,N,z,dt,J,de,re,ge,ue,_t,gt,an,mn,fn,_n,Kt,kt,Dt,ae,rn,nn,Ki,Zi,Yi,tn,en,Ji,Bt,Ut],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.dashboard-title[_ngcontent-%COMP%]{margin-bottom:20px;color:#3f51b5;font-weight:500}.tab-content[_ngcontent-%COMP%]{padding:20px 0;overflow-y:auto}.filters-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-bottom:20px;align-items:center}.mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:200px}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin-top:20px}table[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#0000008a}.stats-container[_ngcontent-%COMP%]{margin-top:20px;margin-bottom:20px}.stats-card[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column}.full-width[_ngcontent-%COMP%]{width:100%}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px;margin-top:16px;padding:16px}.scrollable-content[_ngcontent-%COMP%]{overflow-y:auto;max-height:calc(100% - 60px);padding-right:8px}mat-card-content[_ngcontent-%COMP%]{overflow-y:auto;flex:1}.ride-management-header[_ngcontent-%COMP%]{align-items:center;justify-content:space-between;margin-bottom:16px}.stat-item[_ngcontent-%COMP%]{text-align:center;padding:10px;border-radius:4px;background-color:#3f51b51a}.stat-value[_ngcontent-%COMP%]{font-size:24px;font-weight:500;color:#1976d2}.stat-label[_ngcontent-%COMP%]{font-size:14px;color:#0000008a;margin-top:4px}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto}.desktop-view[_ngcontent-%COMP%]{display:block}.mobile-view[_ngcontent-%COMP%]{display:none}.desktop-filters[_ngcontent-%COMP%]{display:block}.mobile-filter-container[_ngcontent-%COMP%]{display:none}@media (max-width: 768px){.filters-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.mat-form-field[_ngcontent-%COMP%]{width:100%}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}@media (max-width: 600px){.desktop-view[_ngcontent-%COMP%]{display:none}.mobile-view[_ngcontent-%COMP%]{display:block}.desktop-filters[_ngcontent-%COMP%]{display:none}.mobile-filter-container[_ngcontent-%COMP%]{display:block;margin-bottom:16px}.dashboard-container[_ngcontent-%COMP%]{padding:10px}.tab-content[_ngcontent-%COMP%]{padding:10px 0}.filters-container[_ngcontent-%COMP%]{margin-bottom:16px}.mat-tab-body-content[_ngcontent-%COMP%]{overflow:visible;min-height:100vh}mat-tab-group[_ngcontent-%COMP%]{min-height:100vh}.tab-content[_ngcontent-%COMP%]{min-height:calc(100vh - 200px)}mat-card-content[_ngcontent-%COMP%]{min-height:300px;overflow:visible}}.mat-grid-tile[_ngcontent-%COMP%]{min-height:260px}.refresh-info[_ngcontent-%COMP%]{display:flex;align-items:center;margin-left:auto;font-size:14px;color:#0009}.last-refresh[_ngcontent-%COMP%]{margin-right:8px}.auto-refresh-note[_ngcontent-%COMP%]{margin-left:8px;font-style:italic;font-size:12px}@media (max-width: 600px){.refresh-info[_ngcontent-%COMP%]{display:none}}.action-buttons[_ngcontent-%COMP%]{margin-left:20px;display:flex;align-items:center}.close-overlay[_ngcontent-%COMP%]{position:fixed;top:20px;right:20px;z-index:2000;cursor:pointer;background:#fff;border-radius:50%;box-shadow:0 2px 8px #00000026;padding:8px;display:flex;align-items:center;justify-content:center}.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%]{margin:8px 0;border-radius:8px}.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{font-size:14px;padding:12px 16px}.mobile-view[_ngcontent-%COMP%]   .mat-panel-title[_ngcontent-%COMP%]{font-weight:500;font-size:16px;color:#333}.mobile-view[_ngcontent-%COMP%]   .mat-panel-description[_ngcontent-%COMP%]{justify-content:flex-end;align-items:center;margin-left:16px}.mobile-view[_ngcontent-%COMP%]   .user-actions-header[_ngcontent-%COMP%], .mobile-view[_ngcontent-%COMP%]   .ride-actions-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.mobile-view[_ngcontent-%COMP%]   .role-chip[_ngcontent-%COMP%], .mobile-view[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]{font-size:12px;min-height:24px;line-height:24px}.mobile-view[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%], .mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]{padding:0 24px 16px;font-size:14px}.mobile-view[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;line-height:1.4}.mobile-view[_ngcontent-%COMP%]   .mat-action-row[_ngcontent-%COMP%]{justify-content:flex-end;padding:8px 12px 8px 24px;border-top:1px solid rgba(0,0,0,.12)}.mobile-view[_ngcontent-%COMP%]   .mat-action-row[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:8px}.filter-buttons[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;padding:0 16px}.filter-button[_ngcontent-%COMP%]{font-size:12px;min-width:auto;padding:0 12px;height:32px;border-radius:16px}.filter-button.mat-stroked-button[color=primary][_ngcontent-%COMP%]{background-color:#3f51b5;color:#fff;border-color:#3f51b5}.filter-button.mat-stroked-button[_ngcontent-%COMP%]{border-color:#0000001f;color:#000000de}.mobile-search[_ngcontent-%COMP%]{padding:8px 16px 0}.mobile-search[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:14px}.mobile-search[_ngcontent-%COMP%]   .mat-mdc-form-field[_ngcontent-%COMP%]{margin-bottom:0}.mobile-search[_ngcontent-%COMP%]   .mat-mdc-text-field-wrapper[_ngcontent-%COMP%]{height:40px}.mobile-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-size:14px}"]})};export{bn as AdminComponent};
