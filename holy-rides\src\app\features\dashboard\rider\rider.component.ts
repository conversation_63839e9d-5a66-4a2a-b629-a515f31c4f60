import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RideRequestComponent } from './ride-request/ride-request.component';
import { PaymentMethodsComponent } from './payment-methods/payment-methods.component';
import { AuthService } from '../../../core/services/auth.service';
import { RideService } from '../../../core/services/ride.service';
import { PaymentService } from '../../../core/services/payment.service';

import { Ride, RideStatus } from '../../../core/models/ride.model';
import { User } from '../../../core/models/user.model';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../core/services/message.service';
import { RidePaymentComponent } from './ride-payment/ride-payment.component';
import { RideDetailComponent } from '../../../shared/components/ride-detail/ride-detail.component';

import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { Router } from '@angular/router';

@Component({
  selector: 'app-rider',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatTableModule,
    MatChipsModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
    MatExpansionModule,
    RideRequestComponent,
    PaymentMethodsComponent,
    RidePaymentComponent,
    RideDetailComponent,
  ],
  template: `
    <div class="dashboard-container">
      <mat-tab-group>
        <mat-tab label="Request Ride">
          <app-ride-request></app-ride-request>
        </mat-tab>

        <!-- <mat-tab label="Payment Methods">
          <app-payment-methods></app-payment-methods>
        </mat-tab> -->

        <mat-tab label="Ride History">
          <div class="table-container">
            <!-- Desktop View -->
            <div class="desktop-view">
              <table mat-table [dataSource]="rides" class="ride-table">
                <ng-container matColumnDef="pickup_location">
                  <th mat-header-cell *matHeaderCellDef>Pickup</th>
                  <td mat-cell *matCellDef="let ride">{{ride.pickup_location}}</td>
                </ng-container>

                <ng-container matColumnDef="dropoff_location">
                  <th mat-header-cell *matHeaderCellDef>Dropoff</th>
                  <td mat-cell *matCellDef="let ride">{{ride.dropoff_location}}</td>
                </ng-container>

                <ng-container matColumnDef="pickup_time">
                  <th mat-header-cell *matHeaderCellDef>Time</th>
                  <td mat-cell *matCellDef="let ride">{{formatDate(ride.pickup_time)}}</td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let ride">
                    <mat-chip-listbox>
                      <mat-chip [class]="getStatusClass(ride.status)">
                        {{formatStatus(ride.status)}}
                      </mat-chip>
                    </mat-chip-listbox>
                  </td>
                </ng-container>

                <ng-container matColumnDef="payment_status">
                  <th mat-header-cell *matHeaderCellDef>Payment</th>
                  <td mat-cell *matCellDef="let ride">
                    <mat-chip-listbox *ngIf="ride.payment_status">
                      <mat-chip [class]="'payment-status-' + ride.payment_status">
                        {{ride.payment_status}}
                      </mat-chip>
                    </mat-chip-listbox>
                    <span *ngIf="!ride.payment_status">-</span>
                  </td>
                </ng-container>

                <ng-container matColumnDef="fare">
                  <th mat-header-cell *matHeaderCellDef>Fare</th>
                  <td mat-cell *matCellDef="let ride">
                    {{(ride.amount || ride.fare) ? '$' + (ride.amount || ride.fare).toFixed(2) : '-'}}
                  </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let ride">
                    <button mat-icon-button color="warn" *ngIf="ride.status === 'requested'"
                            (click)="cancelRide(ride.id)" matTooltip="Cancel Ride">
                      <mat-icon>cancel</mat-icon>
                    </button>
                    <!-- <button mat-icon-button color="primary" *ngIf="ride.driver_id"
                            (click)="openChat(ride.id)" matTooltip="Message Driver">
                      <mat-icon>chat</mat-icon>
                    </button> -->
                    <button mat-icon-button color="accent" *ngIf="ride.status === 'completed'"
                            (click)="viewPayment(ride)" matTooltip="View Payment">
                      <mat-icon>payment</mat-icon>
                    </button>
                    <button mat-icon-button color="primary"
                            (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              </table>
            </div>

            <!-- Mobile View -->
            <div class="mobile-view">
              <mat-accordion multi>
                <mat-expansion-panel *ngFor="let ride of rides; trackBy: trackByRideId">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      {{ ride.pickup_time | date:'EEE, short' }}
                    </mat-panel-title>
                    <mat-panel-description>
                      <div class="ride-actions-header">
                        <span class="ride-time">{{ ride.pickup_time | date:'shortTime' }}</span>
                        <button mat-icon-button color="warn" *ngIf="ride.status === 'requested'"
                                (click)="cancelRide(ride.id); $event.stopPropagation()" matTooltip="Cancel Ride">
                          <mat-icon>cancel</mat-icon>
                        </button>
                        <button mat-icon-button color="accent" *ngIf="ride.status === 'completed'"
                                (click)="viewPayment(ride); $event.stopPropagation()" matTooltip="View Payment">
                          <mat-icon>payment</mat-icon>
                        </button>
                      </div>
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  <div class="ride-details">
                    <p><strong>To:</strong> {{ ride.dropoff_location }}</p>
                    <p><strong>Time:</strong> {{ ride.pickup_time | date:'short' }}</p>
                    <p><strong>Status:</strong> {{ formatStatus(ride.status) }}</p>
                    <p><strong>Payment:</strong>
                      <span *ngIf="ride.payment_status">{{ ride.payment_status }}</span>
                      <span *ngIf="!ride.payment_status">-</span>
                    </p>
                    <p><strong>Fare:</strong> {{ (ride.amount || ride.fare) ? '$' + (ride.amount || ride.fare)!.toFixed(2) : '-' }}</p>
                  </div>
                  <mat-action-row>
                    <button mat-icon-button color="primary" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                  </mat-action-row>
                </mat-expansion-panel>
              </mat-accordion>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>

    <div *ngIf="selectedRide" class="payment-overlay">
      <app-ride-payment
        [ride]="selectedRide"
        (paymentCompleted)="closePayment()">
      </app-ride-payment>
      <button mat-icon-button class="close-payment-button" (click)="closePayment()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div *ngIf="selectedRideId" class="ride-detail-overlay">
      <app-ride-detail
        [rideId]="selectedRideId"
        [onClose]="closeRideDetails.bind(this)"
        (paymentRequested)="viewPayment($event)"
        (rideUpdated)="onRideUpdated($event)">
      </app-ride-detail>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
        background-color: #f5f5f5;
    }

    .table-container {
      margin: 20px;
    }

    .ride-table {
      width: 100%;
    }

    .status-chip {
      border-radius: 16px;
      padding: 4px 12px;
      color: white;
      font-weight: 500;
    }

    .status-requested {
      background-color: #ff9800;
    }

    .status-assigned {
      background-color: #2196f3;
    }

    .status-in-progress {
      background-color: #673ab7;
    }

    .status-completed {
      background-color: #4caf50;
    }

    .status-canceled {
      background-color: #f44336;
    }

    .payment-status-pending {
      background-color: #ffeb3b;
      color: #000;
    }

    .payment-status-paid {
      background-color: #4caf50;
      color: white;
    }

    .payment-status-failed {
      background-color: #f44336;
      color: white;
    }

    .payment-status-refunded {
      background-color: #9e9e9e;
      color: white;
    }

    .payment-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .close-payment-button {
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: white;
    }

    .ride-detail-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    /* Mobile View Styles */
    .mobile-view {
      display: none;
    }

    .desktop-view {
      display: block;
    }

    .ride-actions-header {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .ride-time {
      font-size: 12px;
      color: #666;
    }

    @media (max-width: 600px) {
      .desktop-view {
        display: none;
      }
      .mobile-view {
        display: block;
      }
      .dashboard-container {
        padding: 0;
      }
      .table-container {
        margin: 0;
      }
      .mat-tab-body-content {
        overflow: hidden;
      }
    }

    .mobile-view .mat-expansion-panel {
      margin: 8px 0;
    }
    .mobile-view .mat-expansion-panel-header {
      font-size: 14px;
    }
    .mobile-view .mat-panel-title {
      font-weight: 500;
    }
    .mobile-view .mat-panel-description {
      justify-content: flex-end;
      align-items: center;
    }
    .mobile-view .ride-details {
      padding: 0 24px 16px;
      font-size: 14px;
    }
    .mobile-view .ride-details p {
      margin: 4px 0;
    }
    .mobile-view .mat-action-row {
      justify-content: flex-end;
      padding: 8px 12px 8px 24px;
    }
  `]
})
export class RiderComponent implements OnInit, OnDestroy {
  rides: Ride[] = [];
  displayedColumns = ['pickup_location', 'dropoff_location', 'pickup_time', 'status', 'payment_status', 'fare', 'actions'];
  currentUser: User | null = null;
  selectedRide: Ride | null = null;
  selectedRideId: string | null = null;
  private ridesSubscription: Subscription | null = null;

  constructor(
    private authService: AuthService,
    private rideService: RideService,
    private messageService: MessageService,
    private paymentService: PaymentService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  async ngOnInit() {
    this.currentUser = await this.authService.getCurrentUser();
    if (this.currentUser) {
      // Initial load of rides
      await this.loadUserRides();

      // Subscribe to ride updates
      this.ridesSubscription = this.rideService.rides$.subscribe((rides: any[]) => {
        if (this.currentUser) {
          this.rides = rides.filter(ride => ride.rider_id === this.currentUser!.id);
        }
      });
    }
  }

  ngOnDestroy() {
    // Clean up subscription when component is destroyed
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }
  }

  async loadUserRides() {
    if (this.currentUser) {
      try {
        this.rides = await this.rideService.getUserRides(this.currentUser.id);
      } catch (error) {
        console.error('Error loading user rides:', error);
        this.snackBar.open('Failed to load rides', 'Close', { duration: 3000 });
      }
    }
  }

  async cancelRide(rideId: string) {
    try {
      const success = await this.rideService.cancelRide(rideId);
      if (success) {
        this.snackBar.open('Ride canceled successfully', 'Close', { duration: 3000 });
        await this.loadUserRides();
      } else {
        throw new Error('Failed to cancel ride');
      }
    } catch (error) {
      console.error('Error canceling ride:', error);
      this.snackBar.open('Failed to cancel ride', 'Close', { duration: 3000 });
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  formatStatus(status: RideStatus): string {
    return status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  }

  getStatusClass(status: RideStatus): string {
    return `status-chip status-${status}`;
  }

  async openChat(rideId: string) {
    try {
      // Navigate to the messages page with this ride's thread
      const thread = await this.messageService.getOrCreateThreadForRide(rideId);
      this.router.navigate(['/dashboard', 'rider', 'messages', thread.id]);
    } catch (error) {
      console.error('Error opening chat:', error);
      this.snackBar.open('Failed to open chat', 'Close', { duration: 3000 });
    }
  }

  viewPayment(ride: Ride) {
    this.selectedRide = ride;
  }

  closePayment() {
    this.selectedRide = null;
    // Refresh rides to get updated payment status
    this.loadUserRides();
  }

  viewRideDetails(rideId: string) {
    this.selectedRideId = rideId;
  }

  closeRideDetails() {
    this.selectedRideId = null;
    // Refresh rides to get updated ratings
    this.loadUserRides();
  }

  onRideUpdated(_ride: Ride) {
    // Reload rides to reflect the updated data
    this.loadUserRides();
  }

  trackByRideId(_index: number, item: Ride): string {
    return item.id;
  }
}
